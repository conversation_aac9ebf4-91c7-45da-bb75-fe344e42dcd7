#!/usr/bin/env python3
"""
AMP CLI Advanced - Complete Multi-agent Programming Command Line Interface
A comprehensive agentic coding tool with all advanced features

Features:
- Complete tool suite (50+ tools)
- Smart sub-agent orchestration
- Token-efficient processing
- Beautiful CLI interface
- Parallel task execution
- Context-aware operations
"""

import os
import sys
import json
import time
import asyncio
import threading
import subprocess
import concurrent.futures
import argparse
import hashlib
import pickle
import shutil
import glob
import re
import ast
import traceback
import logging
import urllib.request
import urllib.parse
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid
import difflib
from contextlib import asynccontextmanager

# Rich for beautiful CLI
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.tree import Tree
from rich.layout import Layout
from rich import box
from rich.align import Align
from rich.columns import Columns
from rich.text import Text

# Third-party imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.tools import Tool
from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.prompts import PromptTemplate
from langchain.callbacks.base import BaseCallbackHandler
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Rich Console
console = Console()

# Configuration and Constants
class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    DELEGATED = "delegated"

class AgentRole(Enum):
    MAIN = "main"
    SUBAGENT = "subagent"
    ORACLE = "oracle"
    SPECIALIST = "specialist"
    FILE_SPECIALIST = "file_specialist"
    CODE_SPECIALIST = "code_specialist"
    TEST_SPECIALIST = "test_specialist"
    WEB_SPECIALIST = "web_specialist"
    TERMINAL_SPECIALIST = "terminal_specialist"

class ToolCategory(Enum):
    FILE_SYSTEM = "file_system"
    TESTING = "testing"
    TERMINAL = "terminal"
    WEB = "web"
    WORKFLOW = "workflow"
    AI_REASONING = "ai_reasoning"
    CODE_ANALYSIS = "code_analysis"

@dataclass
class AgentConfig:
    """Enhanced configuration for agents"""
    model: str = "gemini-2.0-flash-lite"
    temperature: float = 0.1
    max_tokens: Optional[int] = None
    timeout: int = 300
    max_iterations: int = 15
    thinking_budget: int = 500  # Reduced for efficiency
    enable_streaming: bool = True
    token_efficiency: bool = True
    context_compression: bool = True

@dataclass
class Task:
    """Enhanced task representation"""
    id: str
    description: str
    prompt: str
    category: ToolCategory
    status: TaskStatus = TaskStatus.PENDING
    assigned_agent: Optional[str] = None
    parent_task: Optional[str] = None
    subtasks: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    result: Optional[str] = None
    error: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    context: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1
    estimated_tokens: int = 100
    actual_tokens: int = 0
    tools_used: List[str] = field(default_factory=list)

@dataclass
class AgentContext:
    """Enhanced context management"""
    current_directory: str = field(default_factory=os.getcwd)
    active_files: List[str] = field(default_factory=list)
    command_history: List[str] = field(default_factory=list)
    project_structure: Dict = field(default_factory=dict)
    last_error: str = ""
    working_memory: Dict = field(default_factory=dict)
    thread_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    agent_md_config: Dict = field(default_factory=dict)
    file_changes: List[Dict] = field(default_factory=list)
    context_window_usage: int = 0
    tools_used: List[str] = field(default_factory=list)
    session_stats: Dict = field(default_factory=dict)
    performance_metrics: Dict = field(default_factory=dict)
    smart_cache: Dict = field(default_factory=dict)

class TokenOptimizedCallback(BaseCallbackHandler):
    """Callback to track and optimize token usage"""
    
    def __init__(self):
        self.total_tokens = 0
        self.prompt_tokens = 0
        self.completion_tokens = 0
        
    def on_llm_start(self, serialized, prompts, **kwargs):
        # Estimate tokens for prompts
        for prompt in prompts:
            self.prompt_tokens += len(prompt.split()) * 1.3  # Rough estimate
    
    def on_llm_end(self, response, **kwargs):
        # Estimate completion tokens
        if hasattr(response, 'generations'):
            for generation in response.generations:
                for gen in generation:
                    self.completion_tokens += len(gen.text.split()) * 1.3

class AdvancedToolManager:
    """Enhanced tool manager with comprehensive tool suite"""
    
    def __init__(self, context: AgentContext):
        self.context = context
        self.tools = {}
        self.tool_categories = {}
        self.disabled_tools = set()
        self.custom_tools = {}
        self.tool_usage_stats = {}
        self.tool_performance = {}
        
    def register_tool(self, name: str, func: Callable, description: str, 
                     category: ToolCategory, requires_input: bool = True):
        """Register a tool with enhanced metadata"""
        if requires_input:
            tool = Tool(name=name, func=func, description=description)
        else:
            # Wrapper for tools that don't need input
            def wrapper(input_text: str = "") -> str:
                return func()
            tool = Tool(name=name, func=wrapper, description=description)
            
        self.tools[name] = tool
        self.tool_categories[name] = category
        self.tool_usage_stats[name] = {"calls": 0, "success": 0, "failures": 0}
        
    def get_tools_by_category(self, category: ToolCategory) -> List[Tool]:
        """Get tools by category"""
        return [self.tools[name] for name, cat in self.tool_categories.items() 
                if cat == category and name not in self.disabled_tools]
    
    def get_active_tools(self) -> List[Tool]:
        """Get all active tools"""
        return [tool for name, tool in self.tools.items() 
                if name not in self.disabled_tools]
    
    def track_tool_usage(self, tool_name: str, success: bool):
        """Track tool usage statistics"""
        if tool_name in self.tool_usage_stats:
            self.tool_usage_stats[tool_name]["calls"] += 1
            if success:
                self.tool_usage_stats[tool_name]["success"] += 1
            else:
                self.tool_usage_stats[tool_name]["failures"] += 1

class EnhancedSubAgent:
    """Enhanced sub-agent with specialized capabilities"""
    
    def __init__(self, agent_id: str, role: AgentRole, config: AgentConfig, 
                 context: AgentContext, tool_manager: AdvancedToolManager):
        self.agent_id = agent_id
        self.role = role
        self.config = config
        self.context = context
        self.tool_manager = tool_manager
        self.token_callback = TokenOptimizedCallback()
        
        # Initialize LLM with token optimization
        self.llm = ChatGoogleGenerativeAI(
            model=config.model,
            google_api_key=os.getenv("GEMINI_API_KEY"),
            temperature=config.temperature,
            streaming=config.enable_streaming,
            callbacks=[self.token_callback]
        )
        
        self.status = TaskStatus.PENDING
        self.current_task: Optional[Task] = None
        self.specialized_tools = self._get_specialized_tools()
        
    def _get_specialized_tools(self) -> List[Tool]:
        """Get tools specialized for this agent's role"""
        if self.role == AgentRole.FILE_SPECIALIST:
            return self.tool_manager.get_tools_by_category(ToolCategory.FILE_SYSTEM)
        elif self.role == AgentRole.CODE_SPECIALIST:
            return self.tool_manager.get_tools_by_category(ToolCategory.CODE_ANALYSIS)
        elif self.role == AgentRole.TEST_SPECIALIST:
            return self.tool_manager.get_tools_by_category(ToolCategory.TESTING)
        elif self.role == AgentRole.WEB_SPECIALIST:
            return self.tool_manager.get_tools_by_category(ToolCategory.WEB)
        elif self.role == AgentRole.TERMINAL_SPECIALIST:
            return self.tool_manager.get_tools_by_category(ToolCategory.TERMINAL)
        else:
            return self.tool_manager.get_active_tools()
    
    async def execute_task(self, task: Task) -> str:
        """Execute task with enhanced capabilities"""
        self.current_task = task
        self.status = TaskStatus.RUNNING
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        task.assigned_agent = self.agent_id
        
        try:
            # Create optimized prompt
            prompt = self._create_optimized_prompt(task)
            
            # Execute with progress tracking
            with console.status(f"[bold green]Agent {self.agent_id} executing: {task.description}"):
                response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            
            task.result = response.content
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.actual_tokens = self.token_callback.total_tokens
            self.status = TaskStatus.COMPLETED
            
            console.print(f"✅ [green]Agent {self.agent_id} completed task[/green]")
            return response.content
            
        except Exception as e:
            task.error = str(e)
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            self.status = TaskStatus.FAILED
            console.print(f"❌ [red]Agent {self.agent_id} failed: {str(e)}[/red]")
            raise e
    
    def _create_optimized_prompt(self, task: Task) -> str:
        """Create token-efficient prompt"""
        if self.config.token_efficiency:
            # Compress context for efficiency
            context_summary = self._compress_context()
        else:
            context_summary = self._full_context()
            
        prompt = f"""
AGENT: {self.agent_id} | ROLE: {self.role.value}
TASK: {task.description}

CONTEXT:
{context_summary}

SPECIALIZED TOOLS AVAILABLE:
{', '.join([tool.name for tool in self.specialized_tools])}

INSTRUCTIONS:
{task.prompt}

Execute efficiently using available tools. Be concise but thorough.
"""
        return prompt
    
    def _compress_context(self) -> str:
        """Compress context to save tokens"""
        return f"""
DIR: {self.context.current_directory}
FILES: {len(self.context.active_files)} active
RECENT: {self.context.command_history[-1] if self.context.command_history else 'None'}
"""
    
    def _full_context(self) -> str:
        """Full context for complex tasks"""
        return f"""
Directory: {self.context.current_directory}
Active Files: {', '.join(self.context.active_files[-5:]) if self.context.active_files else 'None'}
Recent Commands: {', '.join(self.context.command_history[-3:]) if self.context.command_history else 'None'}
Thread ID: {self.context.thread_id}
Project Config: {bool(self.context.agent_md_config)}
"""

class AmpCLIAdvanced:
    """Advanced AMP CLI with comprehensive capabilities"""
    
    def __init__(self):
        self.config = AgentConfig()
        self.context = AgentContext()
        self.tool_manager = AdvancedToolManager(self.context)
        self.subagents: Dict[str, EnhancedSubAgent] = {}
        self.tasks: Dict[str, Task] = {}
        self.task_queue = asyncio.Queue()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=12)
        self.running = False
        self.session_id = str(uuid.uuid4())
        
        # Initialize main LLM with optimization
        self.token_callback = TokenOptimizedCallback()
        self.llm = ChatGoogleGenerativeAI(
            model=self.config.model,
            google_api_key=os.getenv("GEMINI_API_KEY"),
            temperature=self.config.temperature,
            streaming=self.config.enable_streaming,
            callbacks=[self.token_callback]
        )
        
        # Setup comprehensive tool suite
        self._setup_comprehensive_tools()
        self._load_agent_md_config()
        self._initialize_session()
        
    def _initialize_session(self):
        """Initialize session with beautiful interface"""
        console.clear()
        self._display_welcome_banner()
        
    def _display_welcome_banner(self):
        """Display beautiful welcome banner"""
        banner = Text()
        banner.append("🤖 AMP CLI Advanced\n", style="bold cyan")
        banner.append("Advanced Multi-agent Programming Assistant\n", style="bold white")
        banner.append(f"Session ID: {self.session_id[:8]}\n", style="dim")
        banner.append(f"Model: {self.config.model}", style="green")
        
        panel = Panel(
            Align.center(banner),
            box=box.DOUBLE,
            title="[bold blue]Welcome[/bold blue]",
            subtitle="[dim]Powered by Gemini 2.0 Flash[/dim]"
        )
        console.print(panel)
        
        # Display capabilities
        self._display_capabilities()
    
    def _display_capabilities(self):
        """Display tool capabilities"""
        categories = {
            "🛠️ File System": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                              if cat == ToolCategory.FILE_SYSTEM],
            "🧪 Testing": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                          if cat == ToolCategory.TESTING],
            "💻 Terminal": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                           if cat == ToolCategory.TERMINAL],
            "🌐 Web": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                      if cat == ToolCategory.WEB],
            "🔄 Workflow": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                           if cat == ToolCategory.WORKFLOW],
            "🧠 AI/Reasoning": [f"✓ {name}" for name, cat in self.tool_manager.tool_categories.items() 
                               if cat == ToolCategory.AI_REASONING]
        }
        
        columns = []
        for category, tools in categories.items():
            if tools:
                tool_list = "\n".join(tools[:5])  # Show first 5
                if len(tools) > 5:
                    tool_list += f"\n... and {len(tools) - 5} more"
                columns.append(Panel(tool_list, title=category, border_style="dim"))
        
        if columns:
            console.print(Columns(columns, equal=True, expand=True))
    
    def _setup_comprehensive_tools(self):
        """Setup all comprehensive tools"""
        
        # 🛠️ FILE SYSTEM TOOLS
        self.tool_manager.register_tool(
            "create_file", self.create_file,
            "Create a file with content", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "edit_file", self.edit_file,
            "Edit file by inserting, deleting, or modifying lines", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "create_directory", self.create_directory,
            "Create nested folders/directories", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "read_file", self.read_file,
            "Read specific lines or full content of a file", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "insert_edit_into_file", self.insert_edit_into_file,
            "Insert code/content at specific location in file", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "file_search", self.file_search,
            "Search files by glob patterns", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "grep_search", self.grep_search,
            "Regex/text search inside files", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "list_dir", self.list_dir,
            "List files/folders in a directory", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "list_code_usages", self.list_code_usages,
            "Find where symbols (functions/classes) are used", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "get_changed_files", self.get_changed_files,
            "Show Git diffs of modified files", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "get_errors", self.get_errors,
            "Fetch lint, syntax, or compiler errors", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "semantic_search", self.semantic_search,
            "Natural language search across codebase", ToolCategory.FILE_SYSTEM
        )
        
        self.tool_manager.register_tool(
            "get_project_setup_info", self.get_project_setup_info,
            "Detect framework, language, tooling", ToolCategory.FILE_SYSTEM
        )
        
        # 🧪 TESTING TOOLS
        self.tool_manager.register_tool(
            "test_search", self.test_search,
            "Find tests related to source files", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "test_failure", self.test_failure,
            "Capture and analyze test failure messages", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "run_tests", self.run_tests,
            "Automatically execute test suites", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "autonomous_debugger", self.autonomous_debugger,
            "Trace, identify and fix bugs automatically", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "lint_check", self.lint_check,
            "Run lint/statically analyze code", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "self_repair", self.self_repair,
            "AI-suggested code fixes based on errors", ToolCategory.TESTING
        )
        
        self.tool_manager.register_tool(
            "code_linting_static_analysis", self.code_linting_static_analysis,
            "Combine all error & code checkers", ToolCategory.TESTING
        )
        
        # 💻 TERMINAL TOOLS
        self.tool_manager.register_tool(
            "run_in_terminal", self.run_in_terminal,
            "Run shell commands (Linux/Mac/Windows support)", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "get_terminal_output", self.get_terminal_output,
            "Capture and analyze output from command", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "get_terminal_last_command", self.get_terminal_last_command,
            "What was the last command run", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "get_task_output", self.get_task_output,
            "Get log from running build/dev task", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "create_and_run_task", self.create_and_run_task,
            "Define and execute terminal tasks", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "install_python_packages", self.install_python_packages,
            "pip install packages dynamically", ToolCategory.TERMINAL
        )
        
        self.tool_manager.register_tool(
            "configure_python_environment", self.configure_python_environment,
            "Setup and manage venv/conda", ToolCategory.TERMINAL
        )
        
        # 🌐 WEB TOOLS
        self.tool_manager.register_tool(
            "fetch_webpage", self.fetch_webpage,
            "Scrape webpage content", ToolCategory.WEB
        )
        
        self.tool_manager.register_tool(
            "github_repo", self.github_repo,
            "Search GitHub repos/snippets", ToolCategory.WEB
        )
        
        self.tool_manager.register_tool(
            "semantic_web_search", self.semantic_web_search,
            "Natural lang. Google/Bing/Web search", ToolCategory.WEB
        )
        
        self.tool_manager.register_tool(
            "retrieval_augmented_generation", self.retrieval_augmented_generation,
            "Extract + summarize content from multiple sources", ToolCategory.WEB
        )
        
        # 🔄 WORKFLOW TOOLS
        self.tool_manager.register_tool(
            "create_new_workspace", self.create_new_workspace,
            "Setup a complete dev workspace", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "plan_next_step", self.plan_next_step,
            "Decide next action based on result", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "multi_step_loop", self.multi_step_loop,
            "Code → Run → Fix → Test → Refactor", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "context_aware_refactor", self.context_aware_refactor,
            "Smart restructuring of code", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "modularization", self.modularization,
            "Split code into logical modules", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "code_duplication_removal", self.code_duplication_removal,
            "Remove repeated code", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "function_extraction", self.function_extraction,
            "Extract functions for reuse", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "code_optimizer", self.code_optimizer,
            "Optimize runtime or structure", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "multi_language_translator", self.multi_language_translator,
            "Convert between languages", ToolCategory.WORKFLOW
        )
        
        # 🧠 AI/REASONING TOOLS
        self.tool_manager.register_tool(
            "natural_language_to_code", self.natural_language_to_code,
            "Turn plain English to code", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "intent_recognition", self.intent_recognition,
            "Understand what user wants", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "self_critique", self.self_critique,
            "Evaluate own answers, improve results", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "chain_of_thought_reasoning", self.chain_of_thought_reasoning,
            "Break down complex steps", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "smart_prefetching", self.smart_prefetching,
            "Predict next user intent/code and prepare", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "predict_next_code_block", self.predict_next_code_block,
            "Anticipate what comes next", ToolCategory.AI_REASONING
        )
        
        self.tool_manager.register_tool(
            "analyze_context", self.analyze_context,
            "AST or semantic parser analysis", ToolCategory.AI_REASONING
        )
        
        # AGENT MANAGEMENT TOOLS
        self.tool_manager.register_tool(
            "create_specialist_agent", self.create_specialist_agent,
            "Create specialized sub-agent", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "delegate_parallel_tasks", self.delegate_parallel_tasks,
            "Delegate tasks to multiple agents in parallel", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "orchestrate_workflow", self.orchestrate_workflow,
            "Orchestrate complex multi-agent workflow", ToolCategory.WORKFLOW
        )
        
        self.tool_manager.register_tool(
            "get_context", lambda _="": self.get_context(),
            "Get current context and system information", ToolCategory.AI_REASONING, False
        )
        
        self.tool_manager.register_tool(
            "compact_thread", lambda _="": self.compact_thread(),
            "Compact thread context to manage memory", ToolCategory.AI_REASONING, False
        )

    # 🛠️ FILE SYSTEM TOOL IMPLEMENTATIONS
    
    def create_file(self, input_text: str) -> str:
        """Create a file with content"""
        try:
            parts = input_text.split(" | ", 1)
            if len(parts) != 2:
                return "❌ Format: file_path | content"
            
            file_path, content = parts
            abs_path = os.path.abspath(file_path)
            dir_path = os.path.dirname(abs_path)
            
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)
            
            self.context.file_changes.append({
                "file": abs_path,
                "action": "create",
                "timestamp": datetime.now().isoformat()
            })
            
            return f"✅ File '{file_path}' created successfully ({len(content)} chars)"
            
        except Exception as e:
            return f"❌ Error creating file: {str(e)}"
    
    def edit_file(self, input_text: str) -> str:
        """Edit file by inserting, deleting, or modifying lines"""
        try:
            parts = input_text.split(" | ")
            if len(parts) < 3:
                return "❌ Format: file_path | action | line_num | content (for insert/replace)"
            
            file_path = parts[0]
            action = parts[1].lower()
            line_num = int(parts[2])
            content = parts[3] if len(parts) > 3 else ""
            
            abs_path = os.path.abspath(file_path)
            
            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"
            
            with open(abs_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if action == "insert":
                lines.insert(line_num - 1, content + "\n")
            elif action == "delete":
                if 1 <= line_num <= len(lines):
                    lines.pop(line_num - 1)
            elif action == "replace":
                if 1 <= line_num <= len(lines):
                    lines[line_num - 1] = content + "\n"
            else:
                return "❌ Action must be: insert, delete, or replace"
            
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            self.context.file_changes.append({
                "file": abs_path,
                "action": f"edit_{action}",
                "line": line_num,
                "timestamp": datetime.now().isoformat()
            })
            
            return f"✅ File '{file_path}' edited successfully ({action} at line {line_num})"
            
        except Exception as e:
            return f"❌ Error editing file: {str(e)}"
    
    def create_directory(self, input_text: str) -> str:
        """Create nested folders/directories"""
        try:
            dir_path = input_text.strip()
            abs_path = os.path.abspath(dir_path)
            
            os.makedirs(abs_path, exist_ok=True)
            
            return f"✅ Directory '{dir_path}' created successfully"
            
        except Exception as e:
            return f"❌ Error creating directory: {str(e)}"
    
    def read_file(self, input_text: str) -> str:
        """Read specific lines or full content"""
        try:
            parts = input_text.split(" | ")
            file_path = parts[0]
            
            abs_path = os.path.abspath(file_path)
            
            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"
            
            with open(abs_path, 'r', encoding='utf-8') as f:
                if len(parts) > 1:
                    # Read specific lines
                    line_range = parts[1]
                    if "-" in line_range:
                        start, end = map(int, line_range.split("-"))
                        lines = f.readlines()
                        content = ''.join(lines[start-1:end])
                    else:
                        line_num = int(line_range)
                        lines = f.readlines()
                        content = lines[line_num-1] if line_num <= len(lines) else ""
                else:
                    content = f.read()
            
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)
            
            # Create syntax highlighted display
            file_ext = Path(file_path).suffix.lstrip('.')
            if file_ext:
                syntax = Syntax(content, file_ext, theme="monokai", line_numbers=True)
                with console.capture() as capture:
                    console.print(syntax)
                formatted_content = capture.get()
            else:
                formatted_content = content
            
            return f"✅ File content:\n{formatted_content}"
            
        except Exception as e:
            return f"❌ Error reading file: {str(e)}"
    
    def insert_edit_into_file(self, input_text: str) -> str:
        """Insert code/content at specific location"""
        try:
            parts = input_text.split(" | ", 2)
            if len(parts) != 3:
                return "❌ Format: file_path | line_num | content"
            
            file_path, line_num, content = parts
            return self.edit_file(f"{file_path} | insert | {line_num} | {content}")
            
        except Exception as e:
            return f"❌ Error inserting into file: {str(e)}"
    
    def file_search(self, input_text: str) -> str:
        """Search files by glob patterns"""
        try:
            pattern = input_text.strip()
            search_dir = self.context.current_directory
            
            matches = glob.glob(os.path.join(search_dir, "**", pattern), recursive=True)
            
            if matches:
                result = f"🔍 Found {len(matches)} files matching '{pattern}':\n"
                for match in matches[:20]:  # Limit results
                    rel_path = os.path.relpath(match, search_dir)
                    result += f"  📁 {rel_path}\n"
                if len(matches) > 20:
                    result += f"  ... and {len(matches) - 20} more files"
                return result
            else:
                return f"❌ No files found matching pattern '{pattern}'"
                
        except Exception as e:
            return f"❌ Error searching files: {str(e)}"
    
    def grep_search(self, input_text: str) -> str:
        """Regex/text search inside files"""
        try:
            parts = input_text.split(" | ", 1)
            if len(parts) != 2:
                return "❌ Format: pattern | file_pattern"
            
            pattern, file_pattern = parts
            search_dir = self.context.current_directory
            
            results = []
            file_matches = glob.glob(os.path.join(search_dir, "**", file_pattern), recursive=True)
            
            for file_path in file_matches:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for i, line in enumerate(lines, 1):
                            if re.search(pattern, line, re.IGNORECASE):
                                rel_path = os.path.relpath(file_path, search_dir)
                                results.append(f"  {rel_path}:{i}: {line.strip()}")
                except:
                    continue
            
            if results:
                return f"🔍 Found {len(results)} matches for '{pattern}':\n" + "\n".join(results[:50])
            else:
                return f"❌ No matches found for pattern '{pattern}'"
                
        except Exception as e:
            return f"❌ Error in grep search: {str(e)}"
    
    def list_dir(self, input_text: str) -> str:
        """List files/folders in a directory"""
        try:
            dir_path = input_text.strip() or self.context.current_directory
            abs_path = os.path.abspath(dir_path)
            
            if not os.path.exists(abs_path):
                return f"❌ Directory not found: {dir_path}"
            
            items = []
            for item in sorted(os.listdir(abs_path)):
                item_path = os.path.join(abs_path, item)
                if os.path.isdir(item_path):
                    items.append(f"📁 {item}/")
                else:
                    size = os.path.getsize(item_path)
                    items.append(f"📄 {item} ({size} bytes)")
            
            return f"📂 Directory listing for '{dir_path}':\n" + "\n".join(items)
            
        except Exception as e:
            return f"❌ Error listing directory: {str(e)}"
    
    def list_code_usages(self, input_text: str) -> str:
        """Find where symbols (functions/classes) are used"""
        try:
            parts = input_text.split(" | ")
            symbol = parts[0]
            file_pattern = parts[1] if len(parts) > 1 else "*.py"
            
            search_dir = self.context.current_directory
            matches = glob.glob(os.path.join(search_dir, "**", file_pattern), recursive=True)
            
            usages = []
            for file_path in matches:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for i, line in enumerate(lines, 1):
                            if symbol in line:
                                rel_path = os.path.relpath(file_path, search_dir)
                                usages.append(f"  {rel_path}:{i}: {line.strip()}")
                except:
                    continue
            
            if usages:
                return f"🔍 Found {len(usages)} usages of '{symbol}':\n" + "\n".join(usages[:30])
            else:
                return f"❌ No usages found for symbol '{symbol}'"
                
        except Exception as e:
            return f"❌ Error finding code usages: {str(e)}"
    
    def get_changed_files(self, input_text: str) -> str:
        """Show Git diffs of modified files"""
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=self.context.current_directory,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                changed_files = result.stdout.strip().split('\n') if result.stdout.strip() else []
                if changed_files:
                    return f"📝 Changed files:\n" + "\n".join([f"  {file}" for file in changed_files])
                else:
                    return "✅ No changed files"
            else:
                return "❌ Not a Git repository or Git not available"
                
        except Exception as e:
            return f"❌ Error getting changed files: {str(e)}"
    
    def get_errors(self, input_text: str) -> str:
        """Fetch lint, syntax, or compiler errors"""
        try:
            file_path = input_text.strip()
            abs_path = os.path.abspath(file_path)
            
            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"
            
            errors = []
            
            # Check Python syntax
            if file_path.endswith('.py'):
                try:
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        ast.parse(f.read())
                except SyntaxError as e:
                    errors.append(f"Syntax Error: {e.msg} at line {e.lineno}")
            
            # Run basic linting if available
            try:
                result = subprocess.run(
                    ["python", "-m", "flake8", abs_path],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.stdout:
                    errors.extend(result.stdout.strip().split('\n'))
            except:
                pass
            
            if errors:
                return f"❌ Found {len(errors)} errors:\n" + "\n".join([f"  {error}" for error in errors])
            else:
                return f"✅ No errors found in '{file_path}'"
                
        except Exception as e:
            return f"❌ Error checking for errors: {str(e)}"
    
    def semantic_search(self, input_text: str) -> str:
        """Natural language search across codebase"""
        try:
            query = input_text.strip()
            search_dir = self.context.current_directory
            
            # Simple semantic search using keywords
            keywords = query.lower().split()
            file_patterns = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.md"]
            
            results = []
            for pattern in file_patterns:
                matches = glob.glob(os.path.join(search_dir, "**", pattern), recursive=True)
                for file_path in matches:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            score = sum(1 for keyword in keywords if keyword in content)
                            if score > 0:
                                rel_path = os.path.relpath(file_path, search_dir)
                                results.append((score, rel_path))
                    except:
                        continue
            
            # Sort by relevance
            results.sort(key=lambda x: x[0], reverse=True)
            
            if results:
                return f"🔍 Semantic search results for '{query}':\n" + \
                       "\n".join([f"  📁 {path} (relevance: {score})" for score, path in results[:20]])
            else:
                return f"❌ No relevant files found for query '{query}'"
                
        except Exception as e:
            return f"❌ Error in semantic search: {str(e)}"
    
    def get_project_setup_info(self, input_text: str) -> str:
        """Detect framework, language, tooling"""
        try:
            project_dir = input_text.strip() or self.context.current_directory
            abs_path = os.path.abspath(project_dir)
            
            info = {
                "languages": [],
                "frameworks": [],
                "tools": [],
                "package_managers": [],
                "config_files": []
            }
            
            # Check for common files
            files = os.listdir(abs_path)
            
            # Languages
            if any(f.endswith('.py') for f in files):
                info["languages"].append("Python")
            if any(f.endswith('.js') for f in files):
                info["languages"].append("JavaScript")
            if any(f.endswith('.ts') for f in files):
                info["languages"].append("TypeScript")
            if any(f.endswith('.java') for f in files):
                info["languages"].append("Java")
            if any(f.endswith('.go') for f in files):
                info["languages"].append("Go")
            
            # Package managers
            if "package.json" in files:
                info["package_managers"].append("npm/yarn")
            if "requirements.txt" in files or "setup.py" in files:
                info["package_managers"].append("pip")
            if "Cargo.toml" in files:
                info["package_managers"].append("cargo")
            if "go.mod" in files:
                info["package_managers"].append("go mod")
            
            # Frameworks (check package.json or requirements.txt)
            if "package.json" in files:
                try:
                    with open(os.path.join(abs_path, "package.json"), 'r') as f:
                        package_data = json.load(f)
                        deps = {**package_data.get("dependencies", {}), 
                               **package_data.get("devDependencies", {})}
                        if "react" in deps:
                            info["frameworks"].append("React")
                        if "vue" in deps:
                            info["frameworks"].append("Vue")
                        if "angular" in deps:
                            info["frameworks"].append("Angular")
                        if "express" in deps:
                            info["frameworks"].append("Express")
                except:
                    pass
            
            # Config files
            config_files = ["tsconfig.json", "webpack.config.js", ".eslintrc", 
                           "pytest.ini", "tox.ini", "Dockerfile", ".gitignore"]
            info["config_files"] = [f for f in config_files if f in files]
            
            return f"📋 Project setup information:\n" + json.dumps(info, indent=2)
            
        except Exception as e:
            return f"❌ Error getting project setup info: {str(e)}"

    # 🧪 TESTING TOOL IMPLEMENTATIONS
    
    def test_search(self, input_text: str) -> str:
        """Find tests related to source files"""
        try:
            source_file = input_text.strip()
            search_dir = self.context.current_directory
            
            # Common test patterns
            test_patterns = [
                f"test_{source_file}",
                f"{source_file}_test",
                f"test_{Path(source_file).stem}*",
                f"{Path(source_file).stem}_test*"
            ]
            
            test_files = []
            for pattern in test_patterns:
                matches = glob.glob(os.path.join(search_dir, "**", pattern), recursive=True)
                test_files.extend(matches)
            
            # Also search in common test directories
            test_dirs = ["tests", "test", "__tests__", "spec"]
            for test_dir in test_dirs:
                test_dir_path = os.path.join(search_dir, test_dir)
                if os.path.exists(test_dir_path):
                    for pattern in test_patterns:
                        matches = glob.glob(os.path.join(test_dir_path, "**", pattern), recursive=True)
                        test_files.extend(matches)
            
            if test_files:
                rel_paths = [os.path.relpath(f, search_dir) for f in test_files]
                return f"🧪 Found {len(rel_paths)} test files for '{source_file}':\n" + \
                       "\n".join([f"  📄 {path}" for path in rel_paths])
            else:
                return f"❌ No test files found for '{source_file}'"
                
        except Exception as e:
            return f"❌ Error searching for tests: {str(e)}"
    
    def test_failure(self, input_text: str) -> str:
        """Capture and analyze test failure messages"""
        try:
            # Run tests and capture output
            result = subprocess.run(
                ["python", "-m", "pytest", "-v", "--tb=short"],
                cwd=self.context.current_directory,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            output = result.stdout + result.stderr
            
            if result.returncode != 0:
                # Extract failure information
                failures = []
                lines = output.split('\n')
                in_failure = False
                current_failure = []
                
                for line in lines:
                    if "FAILURES" in line or "FAILED" in line:
                        in_failure = True
                    elif in_failure and line.startswith("="):
                        if current_failure:
                            failures.append('\n'.join(current_failure))
                            current_failure = []
                        in_failure = False
                    elif in_failure:
                        current_failure.append(line)
                
                if failures:
                    return f"❌ Test failures detected:\n" + "\n".join(failures[:3])
                else:
                    return f"❌ Tests failed but no detailed failure info found:\n{output[-500:]}"
            else:
                return "✅ All tests passed!"
                
        except Exception as e:
            return f"❌ Error running tests: {str(e)}"
    
    def run_tests(self, input_text: str) -> str:
        """Automatically execute test suites"""
        try:
            test_path = input_text.strip() or "."
            
            # Try different test runners
            test_commands = [
                ["python", "-m", "pytest", test_path, "-v"],
                ["npm", "test"],
                ["python", "-m", "unittest", "discover", test_path],
                ["go", "test", "./..."],
                ["cargo", "test"]
            ]
            
            for cmd in test_commands:
                try:
                    result = subprocess.run(
                        cmd,
                        cwd=self.context.current_directory,
                        capture_output=True,
                        text=True,
                        timeout=120
                    )
                    
                    if result.returncode == 0:
                        return f"✅ Tests passed using {' '.join(cmd[:2])}:\n{result.stdout[-500:]}"
                    else:
                        return f"❌ Tests failed using {' '.join(cmd[:2])}:\n{result.stderr[-500:]}"
                        
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue
            
            return "❌ No suitable test runner found"
            
        except Exception as e:
            return f"❌ Error running tests: {str(e)}"
    
    def autonomous_debugger(self, input_text: str) -> str:
        """Trace, identify and fix bugs automatically"""
        try:
            error_description = input_text.strip()
            
            # Analyze the error
            debug_steps = [
                "1. Analyzing error description",
                "2. Checking recent file changes",
                "3. Running diagnostics",
                "4. Identifying potential causes",
                "5. Suggesting fixes"
            ]
            
            analysis = []
            
            # Check recent file changes
            if self.context.file_changes:
                recent_changes = self.context.file_changes[-5:]
                analysis.append(f"Recent changes: {len(recent_changes)} files modified")
            
            # Check for common error patterns
            if "syntax" in error_description.lower():
                analysis.append("Potential syntax error detected")
            elif "import" in error_description.lower():
                analysis.append("Potential import/dependency issue")
            elif "undefined" in error_description.lower():
                analysis.append("Potential undefined variable/function")
            
            debug_report = f"🔍 Autonomous Debug Analysis:\n"
            debug_report += "\n".join([f"  {step}" for step in debug_steps])
            debug_report += f"\n\nAnalysis:\n" + "\n".join([f"  • {item}" for item in analysis])
            
            return debug_report
            
        except Exception as e:
            return f"❌ Error in autonomous debugging: {str(e)}"
    
    def lint_check(self, input_text: str) -> str:
        """Run lint/statically analyze code"""
        try:
            file_or_dir = input_text.strip() or "."
            
            linters = {
                "python": ["flake8", "pylint", "mypy"],
                "javascript": ["eslint"],
                "typescript": ["tslint", "eslint"]
            }
            
            results = []
            
            for lang, tools in linters.items():
                for tool in tools:
                    try:
                        result = subprocess.run(
                            [tool, file_or_dir],
                            capture_output=True,
                            text=True,
                            timeout=30
                        )
                        
                        if result.stdout or result.stderr:
                            output = result.stdout or result.stderr
                            results.append(f"{tool}: {output[:200]}...")
                            
                    except (subprocess.TimeoutExpired, FileNotFoundError):
                        continue
            
            if results:
                return f"🔍 Lint check results:\n" + "\n".join(results)
            else:
                return "✅ No linting issues found or no linters available"
                
        except Exception as e:
            return f"❌ Error running lint check: {str(e)}"
    
    def self_repair(self, input_text: str) -> str:
        """AI-suggested code fixes based on errors"""
        try:
            error_info = input_text.strip()
            
            # Use LLM to suggest fixes
            repair_prompt = f"""
Analyze this error and suggest specific code fixes:

ERROR: {error_info}

CONTEXT:
- Current directory: {self.context.current_directory}
- Recent files: {', '.join(self.context.active_files[-3:]) if self.context.active_files else 'None'}

Provide specific, actionable fix suggestions:
"""
            
            response = self.llm.invoke([HumanMessage(content=repair_prompt)])
            
            return f"🔧 AI Self-Repair Suggestions:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in self-repair: {str(e)}"
    
    def code_linting_static_analysis(self, input_text: str) -> str:
        """Combine all error & code checkers"""
        try:
            target = input_text.strip() or "."
            
            # Combine multiple checks
            checks = [
                ("Syntax Check", self.get_errors),
                ("Lint Check", self.lint_check),
                ("Test Results", self.run_tests)
            ]
            
            combined_results = []
            
            for check_name, check_func in checks:
                try:
                    result = check_func(target)
                    combined_results.append(f"{check_name}:\n{result}\n")
                except:
                    combined_results.append(f"{check_name}: ❌ Failed to run\n")
            
            return f"📊 Combined Static Analysis:\n" + "\n".join(combined_results)
            
        except Exception as e:
            return f"❌ Error in combined analysis: {str(e)}"

    # 💻 TERMINAL TOOL IMPLEMENTATIONS
    
    def run_in_terminal(self, input_text: str) -> str:
        """Run shell commands with cross-platform support"""
        try:
            command = input_text.strip()
            
            # Security check for dangerous commands
            dangerous = ["rm -rf", "del /f", "format", "shutdown", "reboot"]
            if any(danger in command.lower() for danger in dangerous):
                return f"⚠️ Dangerous command detected: {command}. Use --dangerously-allow-all to override"
            
            self.context.command_history.append(command)
            
            # Platform-specific execution
            if os.name == 'nt':  # Windows
                if not command.startswith(('powershell', 'cmd')):
                    command = f'powershell -Command "{command}"'
                shell_cmd = command
                shell = True
            else:  # Unix-like
                shell_cmd = command
                shell = True
            
            result = subprocess.run(
                shell_cmd,
                shell=shell,
                capture_output=True,
                text=True,
                timeout=60,
                cwd=self.context.current_directory
            )
            
            output = result.stdout.strip() if result.stdout else ""
            error = result.stderr.strip() if result.stderr else ""
            
            if result.returncode == 0:
                return f"✅ Command executed:\n{output}"
            else:
                return f"❌ Command failed (code {result.returncode}):\n{error}\nOutput: {output}"
                
        except subprocess.TimeoutExpired:
            return "⏰ Command timed out"
        except Exception as e:
            return f"❌ Error executing command: {str(e)}"
    
    def get_terminal_output(self, input_text: str) -> str:
        """Capture and analyze output from command"""
        try:
            command = input_text.strip()
            result = self.run_in_terminal(command)
            
            # Parse and analyze output
            analysis = f"📊 Command Analysis for '{command}':\n"
            analysis += f"Result: {result}\n"
            
            if "✅" in result:
                analysis += "Status: Success\n"
            elif "❌" in result:
                analysis += "Status: Failed\n"
                
            return analysis
            
        except Exception as e:
            return f"❌ Error analyzing terminal output: {str(e)}"
    
    def get_terminal_last_command(self, input_text: str) -> str:
        """Get the last command run"""
        if self.context.command_history:
            last_cmd = self.context.command_history[-1]
            return f"💻 Last command: {last_cmd}"
        else:
            return "❌ No commands have been run in this session"
    
    def get_task_output(self, input_text: str) -> str:
        """Get log from running build/dev task"""
        try:
            task_name = input_text.strip()
            
            # Common task commands
            task_commands = {
                "build": ["npm run build", "python setup.py build", "cargo build", "go build"],
                "dev": ["npm run dev", "npm start", "python manage.py runserver"],
                "test": ["npm test", "python -m pytest", "cargo test", "go test"]
            }
            
            if task_name in task_commands:
                for cmd in task_commands[task_name]:
                    try:
                        result = subprocess.run(
                            cmd.split(),
                            capture_output=True,
                            text=True,
                            timeout=30,
                            cwd=self.context.current_directory
                        )
                        
                        if result.returncode == 0:
                            return f"✅ Task '{task_name}' output:\n{result.stdout[-1000:]}"
                        else:
                            return f"❌ Task '{task_name}' failed:\n{result.stderr[-1000:]}"
                    except:
                        continue
                        
                return f"❌ No suitable command found for task '{task_name}'"
            else:
                return f"❌ Unknown task '{task_name}'. Available: build, dev, test"
                
        except Exception as e:
            return f"❌ Error getting task output: {str(e)}"
    
    def create_and_run_task(self, input_text: str) -> str:
        """Define and execute terminal tasks"""
        try:
            parts = input_text.split(" | ", 1)
            if len(parts) != 2:
                return "❌ Format: task_name | command"
            
            task_name, command = parts
            
            # Store task for future use
            if 'custom_tasks' not in self.context.working_memory:
                self.context.working_memory['custom_tasks'] = {}
            
            self.context.working_memory['custom_tasks'][task_name] = command
            
            # Execute the task
            result = self.run_in_terminal(command)
            
            return f"✅ Created and executed task '{task_name}':\n{result}"
            
        except Exception as e:
            return f"❌ Error creating/running task: {str(e)}"
    
    def install_python_packages(self, input_text: str) -> str:
        """pip install packages dynamically"""
        try:
            packages = input_text.strip()
            
            if not packages:
                return "❌ No packages specified"
            
            # Use pip to install
            result = subprocess.run(
                ["pip", "install"] + packages.split(),
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                return f"✅ Successfully installed packages: {packages}\n{result.stdout[-500:]}"
            else:
                return f"❌ Failed to install packages: {packages}\n{result.stderr[-500:]}"
                
        except Exception as e:
            return f"❌ Error installing packages: {str(e)}"
    
    def configure_python_environment(self, input_text: str) -> str:
        """Setup and manage venv/conda"""
        try:
            action = input_text.strip().lower()
            
            if action == "create_venv":
                result = subprocess.run(
                    ["python", "-m", "venv", "venv"],
                    capture_output=True,
                    text=True,
                    cwd=self.context.current_directory
                )
                
                if result.returncode == 0:
                    return "✅ Virtual environment created successfully"
                else:
                    return f"❌ Failed to create virtual environment: {result.stderr}"
                    
            elif action == "activate":
                if os.name == 'nt':
                    activate_script = os.path.join(self.context.current_directory, "venv", "Scripts", "activate.bat")
                else:
                    activate_script = os.path.join(self.context.current_directory, "venv", "bin", "activate")
                
                if os.path.exists(activate_script):
                    return f"✅ To activate: source {activate_script}"
                else:
                    return "❌ Virtual environment not found"
                    
            else:
                return "❌ Available actions: create_venv, activate"
                
        except Exception as e:
            return f"❌ Error configuring Python environment: {str(e)}"

    # 🌐 WEB TOOL IMPLEMENTATIONS
    
    def fetch_webpage(self, input_text: str) -> str:
        """Scrape webpage content"""
        try:
            url = input_text.strip()
            
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            with urllib.request.urlopen(url, timeout=10) as response:
                content = response.read().decode('utf-8')
            
            # Extract text content (simple)
            text_content = re.sub(r'<[^>]+>', '', content)
            text_content = re.sub(r'\s+', ' ', text_content)
            
            return f"🌐 Webpage content from {url}:\n{text_content[:1000]}..."
            
        except Exception as e:
            return f"❌ Error fetching webpage: {str(e)}"
    
    def github_repo(self, input_text: str) -> str:
        """Search GitHub repos/snippets"""
        try:
            query = input_text.strip()
            
            # Use GitHub search API (simplified)
            search_url = f"https://api.github.com/search/repositories?q={urllib.parse.quote(query)}&sort=stars&order=desc"
            
            with urllib.request.urlopen(search_url, timeout=10) as response:
                data = json.loads(response.read().decode('utf-8'))
            
            repos = data.get('items', [])[:5]  # Top 5 results
            
            if repos:
                result = f"🔍 GitHub repositories for '{query}':\n"
                for repo in repos:
                    result += f"  ⭐ {repo['full_name']} ({repo['stargazers_count']} stars)\n"
                    result += f"     {repo['description'] or 'No description'}\n"
                    result += f"     {repo['html_url']}\n\n"
                return result
            else:
                return f"❌ No repositories found for '{query}'"
                
        except Exception as e:
            return f"❌ Error searching GitHub: {str(e)}"
    
    def semantic_web_search(self, input_text: str) -> str:
        """Natural language web search"""
        try:
            query = input_text.strip()
            
            # Simple web search using DuckDuckGo (no API key required)
            search_url = f"https://duckduckgo.com/html/?q={urllib.parse.quote(query)}"
            
            with urllib.request.urlopen(search_url, timeout=10) as response:
                content = response.read().decode('utf-8')
            
            # Extract search results (simplified)
            results = re.findall(r'<a[^>]*class="result__a"[^>]*href="([^"]*)"[^>]*>([^<]*)</a>', content)
            
            if results:
                search_results = f"🔍 Web search results for '{query}':\n"
                for url, title in results[:5]:
                    search_results += f"  📄 {title}\n     {url}\n\n"
                return search_results
            else:
                return f"❌ No search results found for '{query}'"
                
        except Exception as e:
            return f"❌ Error in web search: {str(e)}"
    
    def retrieval_augmented_generation(self, input_text: str) -> str:
        """Extract + summarize content from multiple sources"""
        try:
            sources = input_text.strip().split(',')
            
            combined_content = []
            
            for source in sources:
                source = source.strip()
                if source.startswith(('http://', 'https://')):
                    # Web source
                    content = self.fetch_webpage(source)
                    combined_content.append(f"From {source}:\n{content}")
                elif os.path.exists(source):
                    # File source
                    content = self.read_file(source)
                    combined_content.append(f"From {source}:\n{content}")
            
            if combined_content:
                # Use LLM to summarize
                summary_prompt = f"""
Summarize and synthesize information from these sources:

{chr(10).join(combined_content)}

Provide a comprehensive summary highlighting key points and connections:
"""
                response = self.llm.invoke([HumanMessage(content=summary_prompt)])
                
                return f"📚 RAG Summary:\n{response.content}"
            else:
                return "❌ No valid sources found"
                
        except Exception as e:
            return f"❌ Error in RAG: {str(e)}"

    # 🔄 WORKFLOW TOOL IMPLEMENTATIONS
    
    def create_new_workspace(self, input_text: str) -> str:
        """Setup a complete dev workspace"""
        try:
            parts = input_text.split(" | ")
            workspace_name = parts[0]
            project_type = parts[1] if len(parts) > 1 else "general"
            
            workspace_path = os.path.join(self.context.current_directory, workspace_name)
            
            # Create directory structure
            directories = {
                "python": ["src", "tests", "docs", "scripts"],
                "web": ["src", "public", "tests", "docs"],
                "general": ["src", "tests", "docs"]
            }
            
            base_dirs = directories.get(project_type, directories["general"])
            
            for dir_name in base_dirs:
                os.makedirs(os.path.join(workspace_path, dir_name), exist_ok=True)
            
            # Create basic files
            files = {
                "README.md": f"# {workspace_name}\n\nProject description here.",
                ".gitignore": "*.pyc\n__pycache__/\n.env\nnode_modules/",
                "requirements.txt": "# Python dependencies\n",
            }
            
            for filename, content in files.items():
                with open(os.path.join(workspace_path, filename), 'w') as f:
                    f.write(content)
            
            return f"✅ Workspace '{workspace_name}' created with {project_type} structure"
            
        except Exception as e:
            return f"❌ Error creating workspace: {str(e)}"
    
    def plan_next_step(self, input_text: str) -> str:
        """Decide next action based on result"""
        try:
            current_result = input_text.strip()
            
            # Use LLM to plan next step
            planning_prompt = f"""
Based on this current result, what should be the next logical step?

CURRENT RESULT: {current_result}

CONTEXT:
- Directory: {self.context.current_directory}
- Active files: {len(self.context.active_files)}
- Recent commands: {len(self.context.command_history)}

Suggest the next 1-3 actionable steps:
"""
            
            response = self.llm.invoke([HumanMessage(content=planning_prompt)])
            
            return f"📋 Next Steps Plan:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error planning next step: {str(e)}"
    
    def multi_step_loop(self, input_text: str) -> str:
        """Code → Run → Fix → Test → Refactor workflow"""
        try:
            workflow_type = input_text.strip() or "standard"
            
            # Define workflow steps
            workflows = {
                "standard": ["code", "run", "fix", "test", "refactor"],
                "tdd": ["test", "code", "run", "refactor"],
                "ci": ["code", "test", "build", "deploy"]
            }
            
            steps = workflows.get(workflow_type, workflows["standard"])
            
            workflow_plan = f"🔄 Multi-step workflow ({workflow_type}):\n"
            for i, step in enumerate(steps, 1):
                workflow_plan += f"  {i}. {step.title()}\n"
            
            # Start executing steps
            workflow_plan += "\n📝 Workflow execution plan created. Use delegate_parallel_tasks to execute."
            
            return workflow_plan
            
        except Exception as e:
            return f"❌ Error creating multi-step loop: {str(e)}"
    
    def context_aware_refactor(self, input_text: str) -> str:
        """Smart restructuring of code"""
        try:
            file_path = input_text.strip()
            
            if not os.path.exists(file_path):
                return f"❌ File not found: {file_path}"
            
            # Read and analyze code
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Use LLM for refactoring suggestions
            refactor_prompt = f"""
Analyze this code and suggest context-aware refactoring improvements:

FILE: {file_path}
CODE:
{code}

PROJECT CONTEXT:
- Directory: {self.context.current_directory}
- Other files: {', '.join(self.context.active_files[-5:]) if self.context.active_files else 'None'}

Suggest specific refactoring improvements:
1. Code structure improvements
2. Performance optimizations
3. Readability enhancements
4. Best practices implementation
"""
            
            response = self.llm.invoke([HumanMessage(content=refactor_prompt)])
            
            return f"🔧 Refactoring suggestions for {file_path}:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in context-aware refactoring: {str(e)}"
    
    def modularization(self, input_text: str) -> str:
        """Split code into logical modules"""
        try:
            file_path = input_text.strip()
            
            if not os.path.exists(file_path):
                return f"❌ File not found: {file_path}"
            
            # Analyze code structure
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Use LLM to suggest modularization
            modular_prompt = f"""
Analyze this code and suggest how to split it into logical modules:

CODE:
{code}

Suggest:
1. What modules to create
2. What functions/classes go in each module
3. How to organize imports
4. File structure recommendations
"""
            
            response = self.llm.invoke([HumanMessage(content=modular_prompt)])
            
            return f"📦 Modularization plan:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in modularization: {str(e)}"
    
    def code_duplication_removal(self, input_text: str) -> str:
        """Remove repeated code"""
        try:
            directory = input_text.strip() or self.context.current_directory
            
            # Find Python files
            python_files = glob.glob(os.path.join(directory, "**", "*.py"), recursive=True)
            
            duplications = []
            
            # Simple duplication detection
            for i, file1 in enumerate(python_files):
                for file2 in python_files[i+1:]:
                    try:
                        with open(file1, 'r', encoding='utf-8') as f1, open(file2, 'r', encoding='utf-8') as f2:
                            lines1 = f1.readlines()
                            lines2 = f2.readlines()
                            
                            # Find similar code blocks
                            for j in range(len(lines1) - 5):
                                block1 = ''.join(lines1[j:j+5])
                                for k in range(len(lines2) - 5):
                                    block2 = ''.join(lines2[k:k+5])
                                    similarity = difflib.SequenceMatcher(None, block1, block2).ratio()
                                    
                                    if similarity > 0.8:  # High similarity
                                        duplications.append({
                                            "file1": os.path.relpath(file1, directory),
                                            "file2": os.path.relpath(file2, directory),
                                            "lines1": f"{j+1}-{j+5}",
                                            "lines2": f"{k+1}-{k+5}",
                                            "similarity": similarity
                                        })
                                        break
                    except:
                        continue
            
            if duplications:
                result = f"🔍 Found {len(duplications)} potential code duplications:\n"
                for dup in duplications[:10]:  # Show first 10
                    result += f"  📄 {dup['file1']}:{dup['lines1']} ↔ {dup['file2']}:{dup['lines2']} ({dup['similarity']:.1%} similar)\n"
                return result
            else:
                return "✅ No significant code duplications found"
                
        except Exception as e:
            return f"❌ Error detecting duplications: {str(e)}"
    
    def function_extraction(self, input_text: str) -> str:
        """Extract functions for reuse"""
        try:
            file_path = input_text.strip()
            
            if not os.path.exists(file_path):
                return f"❌ File not found: {file_path}"
            
            # Analyze code for extraction opportunities
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            extraction_prompt = f"""
Analyze this code and suggest functions that can be extracted for reuse:

CODE:
{code}

Identify:
1. Repeated code patterns that can become functions
2. Long functions that can be broken down
3. Utility functions that can be extracted
4. Suggested function names and signatures
"""
            
            response = self.llm.invoke([HumanMessage(content=extraction_prompt)])
            
            return f"🔧 Function extraction suggestions:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in function extraction: {str(e)}"
    
    def code_optimizer(self, input_text: str) -> str:
        """Optimize runtime or structure"""
        try:
            file_path = input_text.strip()
            
            if not os.path.exists(file_path):
                return f"❌ File not found: {file_path}"
            
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            optimization_prompt = f"""
Analyze this code and suggest optimizations:

CODE:
{code}

Provide optimization suggestions for:
1. Performance improvements
2. Memory usage optimization
3. Algorithm improvements
4. Code structure optimization
5. Specific code changes with examples
"""
            
            response = self.llm.invoke([HumanMessage(content=optimization_prompt)])
            
            return f"⚡ Code optimization suggestions:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in code optimization: {str(e)}"
    
    def multi_language_translator(self, input_text: str) -> str:
        """Convert between programming languages"""
        try:
            parts = input_text.split(" | ")
            if len(parts) != 3:
                return "❌ Format: source_lang | target_lang | code_or_file"
            
            source_lang, target_lang, code_or_file = parts
            
            # Check if it's a file or code
            if os.path.exists(code_or_file):
                with open(code_or_file, 'r', encoding='utf-8') as f:
                    code = f.read()
            else:
                code = code_or_file
            
            translation_prompt = f"""
Translate this {source_lang} code to {target_lang}:

SOURCE CODE ({source_lang}):
{code}

Requirements:
1. Maintain the same functionality
2. Follow {target_lang} best practices
3. Add comments explaining key differences
4. Ensure idiomatic {target_lang} code
"""
            
            response = self.llm.invoke([HumanMessage(content=translation_prompt)])
            
            return f"🔄 Code translation ({source_lang} → {target_lang}):\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in code translation: {str(e)}"

    # 🧠 AI/REASONING TOOL IMPLEMENTATIONS
    
    def natural_language_to_code(self, input_text: str) -> str:
        """Turn plain English to code"""
        try:
            description = input_text.strip()
            
            code_prompt = f"""
Convert this natural language description to working code:

DESCRIPTION: {description}

CONTEXT:
- Project directory: {self.context.current_directory}
- Active files: {', '.join(self.context.active_files[-3:]) if self.context.active_files else 'None'}
- Project type: {self._detect_project_type()}

Generate clean, working code with:
1. Appropriate language choice
2. Best practices
3. Comments explaining the logic
4. Error handling
"""
            
            response = self.llm.invoke([HumanMessage(content=code_prompt)])
            
            return f"💻 Generated code from description:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error generating code: {str(e)}"
    
    def intent_recognition(self, input_text: str) -> str:
        """Understand what user wants"""
        try:
            user_input = input_text.strip()
            
            intent_prompt = f"""
Analyze this user input and identify their intent:

USER INPUT: {user_input}

CONTEXT:
- Current directory: {self.context.current_directory}
- Recent actions: {', '.join(self.context.command_history[-3:]) if self.context.command_history else 'None'}
- Active files: {len(self.context.active_files)} files

Identify:
1. Primary intent/goal
2. Specific actions needed
3. Tools that should be used
4. Suggested next steps
5. Priority level (high/medium/low)
"""
            
            response = self.llm.invoke([HumanMessage(content=intent_prompt)])
            
            return f"🎯 Intent Analysis:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in intent recognition: {str(e)}"
    
    def self_critique(self, input_text: str) -> str:
        """Evaluate own answers and improve results"""
        try:
            previous_result = input_text.strip()
            
            critique_prompt = f"""
Critically evaluate this previous result and suggest improvements:

PREVIOUS RESULT: {previous_result}

Analyze:
1. Accuracy and completeness
2. Areas for improvement
3. Missing information
4. Better approaches
5. Specific improvements to make

Be honest and constructive in the critique.
"""
            
            response = self.llm.invoke([HumanMessage(content=critique_prompt)])
            
            return f"🔍 Self-Critique:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in self-critique: {str(e)}"
    
    def chain_of_thought_reasoning(self, input_text: str) -> str:
        """Break down complex steps with token efficiency"""
        try:
            problem = input_text.strip()
            
            # Use token-efficient reasoning
            if self.config.token_efficiency:
                reasoning_prompt = f"""
Break down this problem step-by-step (be concise):

PROBLEM: {problem}

Steps:
1. 
2. 
3. 
Solution:
"""
            else:
                reasoning_prompt = f"""
Use detailed chain-of-thought reasoning to solve this problem:

PROBLEM: {problem}

Think through this step by step:
1. Understanding the problem
2. Identifying key components
3. Planning the approach
4. Working through the solution
5. Verifying the result

Provide detailed reasoning for each step:
"""
            
            response = self.llm.invoke([HumanMessage(content=reasoning_prompt)])
            
            return f"🧠 Chain of Thought Reasoning:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in reasoning: {str(e)}"
    
    def smart_prefetching(self, input_text: str) -> str:
        """Predict next user intent and prepare"""
        try:
            current_context = input_text.strip()
            
            # Analyze patterns to predict next actions
            prediction_prompt = f"""
Based on current context, predict likely next user actions:

CURRENT CONTEXT: {current_context}
RECENT ACTIONS: {', '.join(self.context.command_history[-5:]) if self.context.command_history else 'None'}
ACTIVE FILES: {', '.join(self.context.active_files[-3:]) if self.context.active_files else 'None'}

Predict top 3 most likely next actions and prepare for them:
"""
            
            response = self.llm.invoke([HumanMessage(content=prediction_prompt)])
            
            # Cache predictions for quick access
            self.context.smart_cache['predictions'] = response.content
            
            return f"🔮 Smart Prefetching Predictions:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in smart prefetching: {str(e)}"
    
    def predict_next_code_block(self, input_text: str) -> str:
        """Anticipate what code comes next"""
        try:
            current_code = input_text.strip()
            
            prediction_prompt = f"""
Based on this code, predict what should come next:

CURRENT CODE:
{current_code}

PROJECT CONTEXT:
{self._detect_project_type()}

Predict the next logical code block(s):
"""
            
            response = self.llm.invoke([HumanMessage(content=prediction_prompt)])
            
            return f"🔮 Next Code Block Prediction:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error predicting next code: {str(e)}"
    
    def analyze_context(self, input_text: str) -> str:
        """AST or semantic parser analysis"""
        try:
            file_path = input_text.strip()
            
            if not os.path.exists(file_path):
                return f"❌ File not found: {file_path}"
            
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            analysis = {
                "file": file_path,
                "size": len(code),
                "lines": len(code.split('\n')),
                "language": Path(file_path).suffix.lstrip('.'),
                "analysis": {}
            }
            
            # Language-specific analysis
            if file_path.endswith('.py'):
                try:
                    tree = ast.parse(code)
                    analysis["analysis"] = {
                        "ast_valid": True,
                        "classes": len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]),
                        "functions": len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
                        "imports": len([n for n in ast.walk(tree) if isinstance(n, (ast.Import, ast.ImportFrom))]),
                        "complexity_score": self._calculate_complexity(tree)
                    }
                except SyntaxError as e:
                    analysis["analysis"] = {"ast_valid": False, "syntax_error": str(e)}
            
            # Use LLM for semantic analysis
            semantic_prompt = f"""
Perform semantic analysis of this code:

CODE:
{code}

AST ANALYSIS:
{json.dumps(analysis, indent=2)}

Provide insights about:
1. Code structure and organization
2. Patterns and design principles used
3. Potential improvements
4. Dependencies and relationships
"""
            
            response = self.llm.invoke([HumanMessage(content=semantic_prompt)])
            
            return f"🔍 Context Analysis:\n{json.dumps(analysis, indent=2)}\n\nSemantic Analysis:\n{response.content}"
            
        except Exception as e:
            return f"❌ Error in context analysis: {str(e)}"

    # ENHANCED AGENT MANAGEMENT TOOLS
    
    def create_specialist_agent(self, input_text: str) -> str:
        """Create specialized sub-agent"""
        try:
            parts = input_text.split(" | ")
            specialty = parts[0].strip().lower()
            task_description = parts[1] if len(parts) > 1 else f"Specialized {specialty} tasks"
            
            # Map specialties to roles
            role_mapping = {
                "file": AgentRole.FILE_SPECIALIST,
                "code": AgentRole.CODE_SPECIALIST,
                "test": AgentRole.TEST_SPECIALIST,
                "web": AgentRole.WEB_SPECIALIST,
                "terminal": AgentRole.TERMINAL_SPECIALIST,
                "oracle": AgentRole.ORACLE
            }
            
            role = role_mapping.get(specialty, AgentRole.SPECIALIST)
            agent_id = f"{specialty}_specialist_{len(self.subagents)}_{int(time.time())}"
            
            # Create specialized context
            specialist_context = AgentContext(
                current_directory=self.context.current_directory,
                thread_id=str(uuid.uuid4()),
                agent_md_config=self.context.agent_md_config.copy()
            )
            
            # Create specialist agent
            specialist = EnhancedSubAgent(agent_id, role, self.config, specialist_context, self.tool_manager)
            self.subagents[agent_id] = specialist
            
            console.print(f"✨ [green]Created {specialty} specialist: {agent_id}[/green]")
            
            return f"✅ Created {specialty} specialist agent: {agent_id}\nSpecialization: {task_description}\nAvailable tools: {len(specialist.specialized_tools)}"
            
        except Exception as e:
            return f"❌ Error creating specialist agent: {str(e)}"
    
    async def delegate_parallel_tasks(self, input_text: str) -> str:
        """Delegate tasks to multiple agents in parallel"""
        try:
            # Parse task descriptions
            task_descriptions = [task.strip() for task in input_text.split("|") if task.strip()]

            if not task_descriptions:
                return "❌ No tasks provided. Format: task1 | task2 | task3"

            # Create task orchestrator
            orchestrator = TaskOrchestrator(self, self.config)

            # Execute tasks in parallel
            results = await orchestrator.execute_parallel_tasks(task_descriptions)

            return f"✅ Parallel task execution completed:\n{results}"

        except Exception as e:
            return f"❌ Error in parallel task delegation: {str(e)}"

    async def orchestrate_workflow(self, input_text: str) -> str:
        """Orchestrate complex multi-agent workflow"""
        try:
            workflow_description = input_text.strip()

            # Use LLM to break down workflow into tasks
            workflow_prompt = f"""
Break down this complex workflow into specific, parallel tasks:

WORKFLOW: {workflow_description}

CONTEXT:
- Directory: {self.context.current_directory}
- Available tools: {len(self.tool_manager.tools)}
- Active files: {len(self.context.active_files)}

Create a list of specific tasks that can be executed by specialized agents:
1. Each task should be independent where possible
2. Specify dependencies between tasks
3. Assign appropriate agent types (file, code, test, web, terminal)
4. Estimate complexity and priority

Format as: agent_type:task_description:dependencies:priority
"""

            response = self.llm.invoke([HumanMessage(content=workflow_prompt)])

            # Parse the workflow plan
            workflow_plan = response.content

            # Execute the orchestrated workflow
            orchestrator = TaskOrchestrator(self, self.config)
            results = await orchestrator.execute_workflow_plan(workflow_plan)

            return f"🎼 Workflow orchestration completed:\n{results}"

        except Exception as e:
            return f"❌ Error in workflow orchestration: {str(e)}"

    def get_context(self) -> str:
        """Get current context and system information"""
        try:
            context_info = {
                "session_id": self.session_id,
                "current_directory": self.context.current_directory,
                "active_files": len(self.context.active_files),
                "recent_files": self.context.active_files[-5:] if self.context.active_files else [],
                "command_history": len(self.context.command_history),
                "recent_commands": self.context.command_history[-3:] if self.context.command_history else [],
                "subagents": len(self.subagents),
                "active_subagents": len([a for a in self.subagents.values() if a.status == TaskStatus.RUNNING]),
                "tasks": len(self.tasks),
                "pending_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
                "tools_available": len(self.tool_manager.tools),
                "token_usage": {
                    "total": self.token_callback.total_tokens,
                    "prompt": self.token_callback.prompt_tokens,
                    "completion": self.token_callback.completion_tokens
                },
                "performance_metrics": self.context.performance_metrics,
                "working_memory": list(self.context.working_memory.keys()) if self.context.working_memory else []
            }

            return f"📊 Current System Context:\n{json.dumps(context_info, indent=2)}"

        except Exception as e:
            return f"❌ Error getting context: {str(e)}"

    def compact_thread(self) -> str:
        """Compact thread context to manage memory"""
        try:
            # Implement context compression
            compressor = ContextCompressor(self.config)

            # Compress command history
            if len(self.context.command_history) > 20:
                compressed_history = compressor.compress_command_history(self.context.command_history)
                self.context.command_history = compressed_history

            # Compress file changes
            if len(self.context.file_changes) > 50:
                compressed_changes = compressor.compress_file_changes(self.context.file_changes)
                self.context.file_changes = compressed_changes

            # Clean up completed tasks
            completed_tasks = [tid for tid, task in self.tasks.items()
                             if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]]

            # Keep only recent completed tasks
            if len(completed_tasks) > 10:
                for tid in completed_tasks[:-10]:
                    del self.tasks[tid]

            # Update context window usage
            self.context.context_window_usage = self._estimate_context_usage()

            return f"✅ Thread compacted successfully. Context usage: {self.context.context_window_usage} tokens"

        except Exception as e:
            return f"❌ Error compacting thread: {str(e)}"

    def _detect_project_type(self) -> str:
        """Detect the type of project in current directory"""
        try:
            files = os.listdir(self.context.current_directory)

            # Check for specific project indicators
            if "package.json" in files:
                return "Node.js/JavaScript"
            elif "requirements.txt" in files or "setup.py" in files or "pyproject.toml" in files:
                return "Python"
            elif "Cargo.toml" in files:
                return "Rust"
            elif "go.mod" in files:
                return "Go"
            elif "pom.xml" in files or "build.gradle" in files:
                return "Java"
            elif "composer.json" in files:
                return "PHP"
            elif "Gemfile" in files:
                return "Ruby"
            elif any(f.endswith('.csproj') for f in files):
                return "C#/.NET"
            else:
                # Check for common file extensions
                extensions = [Path(f).suffix.lower() for f in files if os.path.isfile(os.path.join(self.context.current_directory, f))]
                if '.py' in extensions:
                    return "Python"
                elif '.js' in extensions or '.ts' in extensions:
                    return "JavaScript/TypeScript"
                elif '.java' in extensions:
                    return "Java"
                elif '.go' in extensions:
                    return "Go"
                elif '.rs' in extensions:
                    return "Rust"
                else:
                    return "General"

        except Exception:
            return "Unknown"

    def _calculate_complexity(self, tree) -> int:
        """Calculate complexity score for AST tree"""
        try:
            complexity = 0

            for node in ast.walk(tree):
                # Control flow adds complexity
                if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                    complexity += 1
                # Functions and classes add complexity
                elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                    complexity += 2
                # Nested structures add more complexity
                elif isinstance(node, (ast.ListComp, ast.DictComp, ast.SetComp, ast.GeneratorExp)):
                    complexity += 1
                # Exception handling adds complexity
                elif isinstance(node, (ast.ExceptHandler, ast.Raise)):
                    complexity += 1

            return min(complexity, 100)  # Cap at 100

        except Exception:
            return 5  # Default complexity

    def _load_agent_md_config(self):
        """Load AGENT.md configuration files"""
        try:
            config_files = []
            current_dir = Path(self.context.current_directory)

            # Look for AGENT.md files in current and parent directories
            for parent in [current_dir] + list(current_dir.parents):
                agent_md = parent / "AGENT.md"
                if agent_md.exists():
                    try:
                        with open(agent_md, 'r', encoding='utf-8') as f:
                            content = f.read()
                            config_files.append({
                                "path": str(agent_md),
                                "content": content,
                                "level": len(current_dir.parts) - len(parent.parts)
                            })
                    except Exception:
                        continue

                # Stop at home directory
                if parent == Path.home():
                    break

            # Also check for global config
            global_config = Path.home() / ".config" / "AGENT.md"
            if global_config.exists():
                try:
                    with open(global_config, 'r', encoding='utf-8') as f:
                        content = f.read()
                        config_files.append({
                            "path": str(global_config),
                            "content": content,
                            "level": 999  # Lowest priority
                        })
                except Exception:
                    pass

            # Sort by level (closest to current directory first)
            config_files.sort(key=lambda x: x["level"])

            # Combine configurations
            combined_config = ""
            for config in config_files:
                combined_config += f"\n# From {config['path']}\n{config['content']}\n"

            self.context.agent_md_config = {
                "files": config_files,
                "combined": combined_config,
                "loaded_at": datetime.now().isoformat()
            }

        except Exception as e:
            console.print(f"[yellow]Warning: Could not load AGENT.md config: {str(e)}[/yellow]")
            self.context.agent_md_config = {}

    def _estimate_context_usage(self) -> int:
        """Estimate current context window usage in tokens"""
        try:
            total_tokens = 0

            # Estimate from command history
            if self.context.command_history:
                history_text = " ".join(self.context.command_history)
                total_tokens += len(history_text.split()) * 1.3

            # Estimate from active files
            for file_path in self.context.active_files[-10:]:  # Last 10 files
                try:
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            total_tokens += len(content.split()) * 1.3
                except Exception:
                    continue

            # Estimate from working memory
            if self.context.working_memory:
                memory_text = json.dumps(self.context.working_memory)
                total_tokens += len(memory_text.split()) * 1.3

            # Estimate from agent config
            if self.context.agent_md_config.get("combined"):
                config_text = self.context.agent_md_config["combined"]
                total_tokens += len(config_text.split()) * 1.3

            return int(total_tokens)

        except Exception:
            return 0

class ContextCompressor:
    """Advanced context compression for token efficiency"""

    def __init__(self, config: AgentConfig):
        self.config = config

    def compress_command_history(self, history: List[str]) -> List[str]:
        """Compress command history while preserving important commands"""
        if len(history) <= 20:
            return history

        # Keep recent commands and important ones
        recent = history[-10:]
        important = [cmd for cmd in history[:-10] if self._is_important_command(cmd)]

        # Combine and deduplicate
        compressed = list(dict.fromkeys(important[-5:] + recent))
        return compressed

    def compress_file_changes(self, changes: List[Dict]) -> List[Dict]:
        """Compress file change history"""
        if len(changes) <= 50:
            return changes

        # Group by file and keep only recent changes per file
        file_changes = {}
        for change in changes:
            file_path = change.get("file", "unknown")
            if file_path not in file_changes:
                file_changes[file_path] = []
            file_changes[file_path].append(change)

        # Keep only recent changes per file
        compressed = []
        for file_path, file_change_list in file_changes.items():
            compressed.extend(file_change_list[-3:])  # Keep last 3 changes per file

        return compressed[-50:]  # Keep total under 50

    def _is_important_command(self, command: str) -> bool:
        """Determine if a command is important to preserve"""
        important_patterns = [
            "git", "npm install", "pip install", "cargo build",
            "make", "docker", "kubectl", "terraform"
        ]
        return any(pattern in command.lower() for pattern in important_patterns)

class TokenBudgetManager:
    """Manages token budgets for efficient usage"""

    def __init__(self, config: AgentConfig):
        self.config = config
        self.agent_budgets = {}
        self.usage_history = []

    def allocate_budget(self, agent_id: str, task_complexity: int, agent_role: AgentRole) -> int:
        """Allocate token budget based on task complexity and agent role"""
        base_budget = self.config.thinking_budget

        # Adjust based on agent role
        role_multipliers = {
            AgentRole.MAIN: 1.0,
            AgentRole.SUBAGENT: 0.7,
            AgentRole.ORACLE: 2.0,
            AgentRole.SPECIALIST: 0.8,
            AgentRole.FILE_SPECIALIST: 0.6,
            AgentRole.CODE_SPECIALIST: 0.9,
            AgentRole.TEST_SPECIALIST: 0.7,
            AgentRole.WEB_SPECIALIST: 0.8,
            AgentRole.TERMINAL_SPECIALIST: 0.5
        }

        # Adjust based on task complexity (1-10 scale)
        complexity_multiplier = 0.5 + (task_complexity / 10) * 1.5

        budget = int(base_budget * role_multipliers.get(agent_role, 1.0) * complexity_multiplier)

        self.agent_budgets[agent_id] = budget
        return budget

    def track_usage(self, agent_id: str, tokens_used: int):
        """Track token usage for optimization"""
        self.usage_history.append({
            "agent_id": agent_id,
            "tokens_used": tokens_used,
            "timestamp": datetime.now(),
            "budget": self.agent_budgets.get(agent_id, 0)
        })

        # Keep only recent history
        if len(self.usage_history) > 1000:
            self.usage_history = self.usage_history[-500:]

    def get_efficiency_metrics(self) -> Dict[str, float]:
        """Calculate efficiency metrics"""
        if not self.usage_history:
            return {}

        total_budget = sum(entry["budget"] for entry in self.usage_history)
        total_used = sum(entry["tokens_used"] for entry in self.usage_history)

        return {
            "efficiency_ratio": total_used / total_budget if total_budget > 0 else 0,
            "average_usage": total_used / len(self.usage_history),
            "total_saved": total_budget - total_used
        }

class TaskOrchestrator:
    """Advanced task orchestration with parallel sub-agent execution"""

    def __init__(self, main_agent, config: AgentConfig):
        self.main_agent = main_agent
        self.config = config
        self.token_manager = TokenBudgetManager(config)
        self.active_subagents = {}
        self.task_results = {}

    async def execute_parallel_tasks(self, task_descriptions: List[str]) -> str:
        """Execute multiple tasks in parallel using specialized sub-agents"""
        try:
            # Create tasks from descriptions
            tasks = []
            for i, description in enumerate(task_descriptions):
                task = Task(
                    id=f"parallel_task_{i}_{int(time.time())}",
                    description=description,
                    prompt=description,
                    category=self._determine_task_category(description),
                    priority=1,
                    estimated_tokens=self._estimate_task_tokens(description)
                )
                tasks.append(task)

            # Execute tasks in parallel
            results = await self._execute_tasks_parallel(tasks)

            # Aggregate results
            aggregated_result = self._aggregate_results(results)

            return aggregated_result

        except Exception as e:
            return f"❌ Error in parallel task execution: {str(e)}"

    async def execute_workflow_plan(self, workflow_plan: str) -> str:
        """Execute a complex workflow plan with dependencies"""
        try:
            # Parse workflow plan
            workflow_tasks = self._parse_workflow_plan(workflow_plan)

            # Build dependency graph
            dependency_graph = self._build_dependency_graph(workflow_tasks)

            # Execute tasks respecting dependencies
            results = await self._execute_with_dependencies(dependency_graph)

            # Generate comprehensive report
            report = self._generate_workflow_report(results)

            return report

        except Exception as e:
            return f"❌ Error in workflow execution: {str(e)}"

    async def _execute_tasks_parallel(self, tasks: List[Task]) -> Dict[str, str]:
        """Execute tasks in parallel with proper resource management"""
        results = {}

        # Create semaphore to limit concurrent agents
        max_concurrent = min(len(tasks), self.config.max_iterations // 2, 8)
        semaphore = asyncio.Semaphore(max_concurrent)

        async def execute_single_task(task: Task) -> Tuple[str, str]:
            async with semaphore:
                try:
                    # Create specialized sub-agent
                    agent_role = self._determine_agent_role(task.category)
                    subagent = await self._create_subagent(task, agent_role)

                    # Execute task
                    result = await subagent.execute_task(task)

                    # Track token usage
                    self.token_manager.track_usage(subagent.agent_id, task.actual_tokens)

                    return task.id, result

                except Exception as e:
                    return task.id, f"❌ Task failed: {str(e)}"

        # Execute all tasks concurrently
        task_futures = [execute_single_task(task) for task in tasks]
        completed_results = await asyncio.gather(*task_futures, return_exceptions=True)

        # Process results
        for result in completed_results:
            if isinstance(result, Exception):
                console.print(f"[red]Task execution error: {str(result)}[/red]")
            else:
                task_id, task_result = result
                results[task_id] = task_result

        return results

    async def _create_subagent(self, task: Task, role: AgentRole) -> EnhancedSubAgent:
        """Create a specialized sub-agent for the task"""
        agent_id = f"{role.value}_{task.id}_{int(time.time())}"

        # Create specialized context
        subagent_context = AgentContext(
            current_directory=self.main_agent.context.current_directory,
            thread_id=str(uuid.uuid4()),
            agent_md_config=self.main_agent.context.agent_md_config.copy()
        )

        # Allocate token budget
        complexity = self._estimate_task_complexity(task)
        budget = self.token_manager.allocate_budget(agent_id, complexity, role)

        # Create optimized config for subagent
        subagent_config = AgentConfig(
            model=self.config.model,
            temperature=self.config.temperature,
            thinking_budget=budget,
            token_efficiency=True,  # Always use token efficiency for subagents
            context_compression=True
        )

        # Create and register subagent
        subagent = EnhancedSubAgent(agent_id, role, subagent_config, subagent_context, self.main_agent.tool_manager)
        self.active_subagents[agent_id] = subagent

        return subagent

    def _determine_task_category(self, description: str) -> ToolCategory:
        """Determine the appropriate tool category for a task"""
        description_lower = description.lower()

        if any(keyword in description_lower for keyword in ["file", "create", "edit", "read", "write", "directory"]):
            return ToolCategory.FILE_SYSTEM
        elif any(keyword in description_lower for keyword in ["test", "debug", "lint", "check", "verify"]):
            return ToolCategory.TESTING
        elif any(keyword in description_lower for keyword in ["run", "execute", "command", "terminal", "shell"]):
            return ToolCategory.TERMINAL
        elif any(keyword in description_lower for keyword in ["web", "http", "api", "fetch", "download", "search"]):
            return ToolCategory.WEB
        elif any(keyword in description_lower for keyword in ["workflow", "orchestrate", "coordinate", "manage"]):
            return ToolCategory.WORKFLOW
        elif any(keyword in description_lower for keyword in ["analyze", "think", "reason", "plan", "understand"]):
            return ToolCategory.AI_REASONING
        else:
            return ToolCategory.CODE_ANALYSIS

    def _determine_agent_role(self, category: ToolCategory) -> AgentRole:
        """Determine the appropriate agent role for a category"""
        role_mapping = {
            ToolCategory.FILE_SYSTEM: AgentRole.FILE_SPECIALIST,
            ToolCategory.TESTING: AgentRole.TEST_SPECIALIST,
            ToolCategory.TERMINAL: AgentRole.TERMINAL_SPECIALIST,
            ToolCategory.WEB: AgentRole.WEB_SPECIALIST,
            ToolCategory.WORKFLOW: AgentRole.SPECIALIST,
            ToolCategory.AI_REASONING: AgentRole.ORACLE,
            ToolCategory.CODE_ANALYSIS: AgentRole.CODE_SPECIALIST
        }
        return role_mapping.get(category, AgentRole.SPECIALIST)

    def _estimate_task_tokens(self, description: str) -> int:
        """Estimate token requirements for a task"""
        base_tokens = len(description.split()) * 1.5

        # Adjust based on task complexity indicators
        complexity_indicators = ["analyze", "complex", "multiple", "comprehensive", "detailed"]
        complexity_bonus = sum(10 for indicator in complexity_indicators if indicator in description.lower())

        return int(base_tokens + complexity_bonus)

    def _estimate_task_complexity(self, task: Task) -> int:
        """Estimate task complexity on a 1-10 scale"""
        description = task.description.lower()
        complexity = 3  # Base complexity

        # Increase complexity based on keywords
        if any(word in description for word in ["complex", "comprehensive", "multiple", "analyze"]):
            complexity += 2
        if any(word in description for word in ["refactor", "optimize", "restructure"]):
            complexity += 3
        if any(word in description for word in ["debug", "fix", "troubleshoot"]):
            complexity += 2
        if any(word in description for word in ["create", "build", "implement"]):
            complexity += 1

        return min(complexity, 10)

    def _aggregate_results(self, results: Dict[str, str]) -> str:
        """Aggregate results from multiple parallel tasks"""
        if not results:
            return "❌ No results to aggregate"

        aggregated = "🎯 Parallel Task Execution Results:\n\n"

        successful_tasks = 0
        failed_tasks = 0

        for task_id, result in results.items():
            if result.startswith("❌"):
                failed_tasks += 1
                aggregated += f"❌ Task {task_id}: FAILED\n{result}\n\n"
            else:
                successful_tasks += 1
                aggregated += f"✅ Task {task_id}: SUCCESS\n{result}\n\n"

        # Add summary
        aggregated += f"📊 Summary: {successful_tasks} successful, {failed_tasks} failed\n"

        # Add efficiency metrics
        metrics = self.token_manager.get_efficiency_metrics()
        if metrics:
            aggregated += f"⚡ Token Efficiency: {metrics.get('efficiency_ratio', 0):.2%}\n"

        return aggregated

    def _parse_workflow_plan(self, plan: str) -> List[Dict]:
        """Parse workflow plan into structured tasks"""
        tasks = []
        lines = plan.strip().split('\n')

        for line in lines:
            line = line.strip()
            if ':' in line and not line.startswith('#'):
                try:
                    parts = line.split(':')
                    if len(parts) >= 4:
                        agent_type = parts[0].strip()
                        description = parts[1].strip()
                        dependencies = [dep.strip() for dep in parts[2].split(',') if dep.strip()]
                        priority = int(parts[3].strip()) if parts[3].strip().isdigit() else 1

                        tasks.append({
                            "agent_type": agent_type,
                            "description": description,
                            "dependencies": dependencies,
                            "priority": priority
                        })
                except Exception:
                    continue

        return tasks

    def _build_dependency_graph(self, workflow_tasks: List[Dict]) -> Dict:
        """Build dependency graph for workflow execution"""
        graph = {}

        for i, task in enumerate(workflow_tasks):
            task_id = f"workflow_task_{i}"
            graph[task_id] = {
                "task": task,
                "dependencies": task.get("dependencies", []),
                "dependents": [],
                "status": "pending"
            }

        # Build dependent relationships
        for task_id, task_info in graph.items():
            for dep in task_info["dependencies"]:
                if dep in graph:
                    graph[dep]["dependents"].append(task_id)

        return graph

    async def _execute_with_dependencies(self, dependency_graph: Dict) -> Dict[str, str]:
        """Execute tasks respecting dependency constraints"""
        results = {}
        completed = set()

        while len(completed) < len(dependency_graph):
            # Find tasks ready to execute (all dependencies completed)
            ready_tasks = []
            for task_id, task_info in dependency_graph.items():
                if (task_info["status"] == "pending" and
                    all(dep in completed for dep in task_info["dependencies"])):
                    ready_tasks.append(task_id)

            if not ready_tasks:
                # Circular dependency or other issue
                break

            # Execute ready tasks in parallel
            task_futures = []
            for task_id in ready_tasks:
                task_info = dependency_graph[task_id]
                task_info["status"] = "running"

                # Create task object
                task = Task(
                    id=task_id,
                    description=task_info["task"]["description"],
                    prompt=task_info["task"]["description"],
                    category=self._determine_task_category(task_info["task"]["description"]),
                    priority=task_info["task"]["priority"]
                )

                task_futures.append(self._execute_single_workflow_task(task))

            # Wait for completion
            completed_results = await asyncio.gather(*task_futures, return_exceptions=True)

            # Process results
            for i, result in enumerate(completed_results):
                task_id = ready_tasks[i]
                if isinstance(result, Exception):
                    results[task_id] = f"❌ Task failed: {str(result)}"
                else:
                    results[task_id] = result

                dependency_graph[task_id]["status"] = "completed"
                completed.add(task_id)

        return results

    async def _execute_single_workflow_task(self, task: Task) -> str:
        """Execute a single workflow task"""
        try:
            agent_role = self._determine_agent_role(task.category)
            subagent = await self._create_subagent(task, agent_role)
            result = await subagent.execute_task(task)
            return result
        except Exception as e:
            return f"❌ Workflow task failed: {str(e)}"

    def _generate_workflow_report(self, results: Dict[str, str]) -> str:
        """Generate comprehensive workflow execution report"""
        report = "🎼 Workflow Execution Report:\n\n"

        successful = sum(1 for result in results.values() if not result.startswith("❌"))
        failed = len(results) - successful

        report += f"📊 Execution Summary:\n"
        report += f"  ✅ Successful tasks: {successful}\n"
        report += f"  ❌ Failed tasks: {failed}\n"
        report += f"  📈 Success rate: {successful/len(results)*100:.1f}%\n\n"

        report += "📋 Task Results:\n"
        for task_id, result in results.items():
            status = "✅" if not result.startswith("❌") else "❌"
            report += f"{status} {task_id}:\n{result}\n\n"

        # Add performance metrics
        metrics = self.token_manager.get_efficiency_metrics()
        if metrics:
            report += f"⚡ Performance Metrics:\n"
            report += f"  Token Efficiency: {metrics.get('efficiency_ratio', 0):.2%}\n"
            report += f"  Average Usage: {metrics.get('average_usage', 0):.0f} tokens\n"
            report += f"  Total Saved: {metrics.get('total_saved', 0):.0f} tokens\n"

        return report

class CLIInterface:
    """Advanced CLI interface with interactive and batch modes"""

    def __init__(self, amp_system: AmpCLIAdvanced):
        self.amp = amp_system
        self.running = False

    async def start_interactive_mode(self):
        """Start interactive chat mode"""
        self.running = True
        console.print("\n[bold green]🚀 AMP CLI Advanced - Interactive Mode[/bold green]")
        console.print("[dim]Type 'help' for commands, 'exit' to quit[/dim]\n")

        while self.running:
            try:
                # Get user input with rich prompt
                user_input = console.input("[bold cyan]amp>[/bold cyan] ")

                if not user_input.strip():
                    continue

                # Handle special commands
                if user_input.lower() in ['exit', 'quit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'status':
                    result = self.amp.get_context()
                    console.print(result)
                    continue
                elif user_input.lower() == 'compact':
                    result = self.amp.compact_thread()
                    console.print(result)
                    continue
                elif user_input.startswith('/'):
                    await self._handle_slash_command(user_input)
                    continue

                # Process regular input
                await self._process_user_input(user_input)

            except KeyboardInterrupt:
                console.print("\n[yellow]Use 'exit' to quit gracefully[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {str(e)}[/red]")

        console.print("\n[bold green]👋 Goodbye![/bold green]")

    async def execute_batch_mode(self, tasks: List[str]):
        """Execute tasks in batch mode"""
        console.print(f"\n[bold green]🔄 Executing {len(tasks)} tasks in batch mode[/bold green]")

        results = []
        for i, task in enumerate(tasks, 1):
            console.print(f"\n[bold cyan]Task {i}/{len(tasks)}:[/bold cyan] {task}")

            try:
                result = await self._process_user_input(task)
                results.append({"task": task, "result": result, "success": True})
            except Exception as e:
                error_msg = f"❌ Error: {str(e)}"
                console.print(f"[red]{error_msg}[/red]")
                results.append({"task": task, "result": error_msg, "success": False})

        # Generate batch report
        successful = sum(1 for r in results if r["success"])
        console.print(f"\n[bold green]📊 Batch Execution Complete:[/bold green]")
        console.print(f"  ✅ Successful: {successful}/{len(tasks)}")
        console.print(f"  ❌ Failed: {len(tasks) - successful}/{len(tasks)}")

        return results

    async def _process_user_input(self, user_input: str) -> str:
        """Process user input and determine appropriate action"""
        try:
            # Use intent recognition to understand user request
            intent_result = self.amp.intent_recognition(user_input)

            # Check if this should be delegated to parallel agents
            if self._should_use_parallel_execution(user_input):
                # Break down into parallel tasks
                tasks = self._extract_parallel_tasks(user_input)
                if len(tasks) > 1:
                    result = await self.amp.delegate_parallel_tasks(" | ".join(tasks))
                    console.print(result)
                    return result

            # Check if this needs workflow orchestration
            if self._should_use_workflow_orchestration(user_input):
                result = await self.amp.orchestrate_workflow(user_input)
                console.print(result)
                return result

            # Use appropriate tool based on intent
            tool_name = self._determine_best_tool(user_input)
            if tool_name and tool_name in self.amp.tool_manager.tools:
                tool = self.amp.tool_manager.tools[tool_name]

                with console.status(f"[bold green]Executing {tool_name}..."):
                    result = tool.func(user_input)

                console.print(result)
                return result
            else:
                # Use general AI reasoning
                result = self.amp.natural_language_to_code(user_input)
                console.print(result)
                return result

        except Exception as e:
            error_msg = f"❌ Error processing input: {str(e)}"
            console.print(f"[red]{error_msg}[/red]")
            return error_msg

    def _should_use_parallel_execution(self, user_input: str) -> bool:
        """Determine if input should use parallel execution"""
        parallel_indicators = [
            "multiple", "several", "each", "all", "parallel",
            "simultaneously", "at once", "concurrently"
        ]
        return any(indicator in user_input.lower() for indicator in parallel_indicators)

    def _should_use_workflow_orchestration(self, user_input: str) -> bool:
        """Determine if input needs workflow orchestration"""
        workflow_indicators = [
            "workflow", "orchestrate", "coordinate", "manage",
            "complex", "multi-step", "process", "pipeline"
        ]
        return any(indicator in user_input.lower() for indicator in workflow_indicators)

    def _extract_parallel_tasks(self, user_input: str) -> List[str]:
        """Extract individual tasks for parallel execution"""
        # Simple heuristic - look for task separators
        separators = [" and ", ", ", "; ", " then ", " also "]

        tasks = [user_input]
        for separator in separators:
            new_tasks = []
            for task in tasks:
                new_tasks.extend(task.split(separator))
            tasks = new_tasks

        # Clean up tasks
        tasks = [task.strip() for task in tasks if task.strip()]

        # If we couldn't split meaningfully, return original
        if len(tasks) <= 1:
            return [user_input]

        return tasks

    def _determine_best_tool(self, user_input: str) -> Optional[str]:
        """Determine the best tool for the user input"""
        input_lower = user_input.lower()

        # File operations
        if any(word in input_lower for word in ["create file", "edit file", "read file", "write file"]):
            if "create" in input_lower:
                return "create_file"
            elif "edit" in input_lower:
                return "edit_file"
            elif "read" in input_lower:
                return "read_file"

        # Directory operations
        if any(word in input_lower for word in ["create directory", "mkdir", "list directory", "ls"]):
            if "create" in input_lower or "mkdir" in input_lower:
                return "create_directory"
            elif "list" in input_lower or "ls" in input_lower:
                return "list_dir"

        # Search operations
        if any(word in input_lower for word in ["search", "find", "grep", "look for"]):
            if "file" in input_lower:
                return "file_search"
            elif "code" in input_lower or "function" in input_lower:
                return "list_code_usages"
            else:
                return "semantic_search"

        # Terminal operations
        if any(word in input_lower for word in ["run", "execute", "command", "terminal"]):
            return "run_in_terminal"

        # Testing operations
        if any(word in input_lower for word in ["test", "debug", "lint", "check"]):
            if "test" in input_lower:
                return "run_tests"
            elif "lint" in input_lower:
                return "lint_check"
            elif "debug" in input_lower:
                return "autonomous_debugger"

        # Web operations
        if any(word in input_lower for word in ["fetch", "download", "web", "http", "url"]):
            return "fetch_webpage"

        return None

    async def _handle_slash_command(self, command: str):
        """Handle slash commands"""
        cmd = command[1:].lower().strip()

        if cmd == "help":
            self._show_help()
        elif cmd == "status":
            result = self.amp.get_context()
            console.print(result)
        elif cmd == "compact":
            result = self.amp.compact_thread()
            console.print(result)
        elif cmd == "agents":
            self._show_agents()
        elif cmd == "tools":
            self._show_tools()
        elif cmd == "metrics":
            self._show_metrics()
        else:
            console.print(f"[red]Unknown command: {cmd}[/red]")

    def _show_help(self):
        """Show help information"""
        help_text = """
[bold cyan]AMP CLI Advanced - Help[/bold cyan]

[bold]Commands:[/bold]
  help, /help     - Show this help
  status, /status - Show system status
  compact, /compact - Compact thread context
  /agents         - Show active agents
  /tools          - Show available tools
  /metrics        - Show performance metrics
  exit, quit, q   - Exit the program

[bold]Features:[/bold]
  • Parallel task execution - Use words like 'multiple', 'parallel', 'each'
  • Workflow orchestration - Use words like 'workflow', 'orchestrate', 'complex'
  • Smart tool selection - Automatically chooses the best tool for your request
  • Token optimization - Efficient context management and compression
  • Sub-agent specialization - Specialized agents for different task types

[bold]Examples:[/bold]
  "Create multiple files: config.py, utils.py, main.py"
  "Run tests in parallel for all Python files"
  "Orchestrate a complex deployment workflow"
  "Analyze and refactor the entire codebase"
"""
        console.print(help_text)

    def _show_agents(self):
        """Show active agents"""
        if not self.amp.subagents:
            console.print("[yellow]No active sub-agents[/yellow]")
            return

        table = Table(title="Active Sub-Agents")
        table.add_column("Agent ID", style="cyan")
        table.add_column("Role", style="green")
        table.add_column("Status", style="yellow")
        table.add_column("Tools", style="blue")

        for agent_id, agent in self.amp.subagents.items():
            table.add_row(
                agent_id,
                agent.role.value,
                agent.status.value,
                str(len(agent.specialized_tools))
            )

        console.print(table)

    def _show_tools(self):
        """Show available tools"""
        categories = {}
        for name, category in self.amp.tool_manager.tool_categories.items():
            if category not in categories:
                categories[category] = []
            categories[category].append(name)

        for category, tools in categories.items():
            console.print(f"\n[bold]{category.value.replace('_', ' ').title()}:[/bold]")
            for tool in sorted(tools):
                console.print(f"  • {tool}")

    def _show_metrics(self):
        """Show performance metrics"""
        metrics = {
            "Session ID": self.amp.session_id[:8],
            "Total Tokens": self.amp.token_callback.total_tokens,
            "Active Files": len(self.amp.context.active_files),
            "Commands Run": len(self.amp.context.command_history),
            "Sub-agents": len(self.amp.subagents),
            "Tasks": len(self.amp.tasks),
            "Tools Available": len(self.amp.tool_manager.tools)
        }

        table = Table(title="Performance Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")

        for metric, value in metrics.items():
            table.add_row(metric, str(value))

        console.print(table)

async def main():
    """Main execution function"""
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(
            description="AMP CLI Advanced - Complete Multi-agent Programming Assistant",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  amp                                    # Interactive mode
  amp -t "Create a Python web app"      # Single task
  amp -b task1.txt task2.txt            # Batch mode
  amp --parallel "task1 | task2 | task3" # Parallel execution
  amp --workflow "complex deployment"    # Workflow orchestration
  amp --optimize                         # Token optimization mode
            """
        )

        parser.add_argument("-t", "--task", help="Execute a single task")
        parser.add_argument("-b", "--batch", nargs="+", help="Execute tasks from files")
        parser.add_argument("--parallel", help="Execute tasks in parallel (separated by |)")
        parser.add_argument("--workflow", help="Execute complex workflow")
        parser.add_argument("--optimize", action="store_true", help="Enable aggressive token optimization")
        parser.add_argument("--model", default="gemini-2.0-flash-lite", help="LLM model to use")
        parser.add_argument("--temperature", type=float, default=0.1, help="Model temperature")
        parser.add_argument("--max-agents", type=int, default=8, help="Maximum concurrent sub-agents")
        parser.add_argument("--token-budget", type=int, default=500, help="Token budget per agent")
        parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
        parser.add_argument("--config", help="Configuration file path")
        parser.add_argument("--session", help="Resume specific session")

        args = parser.parse_args()

        # Configure logging
        if args.verbose:
            logging.basicConfig(level=logging.INFO)

        # Initialize AMP system with custom configuration
        config = AgentConfig(
            model=args.model,
            temperature=args.temperature,
            thinking_budget=args.token_budget,
            token_efficiency=args.optimize,
            context_compression=args.optimize
        )

        amp_system = AmpCLIAdvanced()
        amp_system.config = config

        # Initialize CLI interface
        cli = CLIInterface(amp_system)

        # Execute based on arguments
        if args.task:
            # Single task mode
            console.print(f"[bold green]🎯 Executing task:[/bold green] {args.task}")
            result = await cli._process_user_input(args.task)
            console.print(f"\n[bold green]✅ Task completed[/bold green]")

        elif args.parallel:
            # Parallel execution mode
            console.print(f"[bold green]⚡ Parallel execution:[/bold green] {args.parallel}")
            result = await amp_system.delegate_parallel_tasks(args.parallel)
            console.print(result)

        elif args.workflow:
            # Workflow orchestration mode
            console.print(f"[bold green]🎼 Workflow orchestration:[/bold green] {args.workflow}")
            result = await amp_system.orchestrate_workflow(args.workflow)
            console.print(result)

        elif args.batch:
            # Batch mode
            tasks = []
            for file_path in args.batch:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_tasks = [line.strip() for line in f.readlines() if line.strip()]
                        tasks.extend(file_tasks)
                except Exception as e:
                    console.print(f"[red]Error reading {file_path}: {str(e)}[/red]")

            if tasks:
                await cli.execute_batch_mode(tasks)
            else:
                console.print("[red]No tasks found in batch files[/red]")

        else:
            # Interactive mode (default)
            await cli.start_interactive_mode()

    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Fatal error: {str(e)}[/red]")
        if args.verbose:
            console.print(f"[red]{traceback.format_exc()}[/red]")
        sys.exit(1)

def setup_environment():
    """Setup environment and dependencies"""
    try:
        # Check for required environment variables
        if not os.getenv("GEMINI_API_KEY"):
            console.print("[red]Error: GEMINI_API_KEY environment variable not set[/red]")
            console.print("[yellow]Please set your Gemini API key:[/yellow]")
            console.print("export GEMINI_API_KEY=your_api_key_here")
            sys.exit(1)

        # Create necessary directories
        config_dir = Path.home() / ".config" / "amp-cli-advanced"
        config_dir.mkdir(parents=True, exist_ok=True)

        # Initialize session directory
        session_dir = config_dir / "sessions"
        session_dir.mkdir(exist_ok=True)

        return config_dir

    except Exception as e:
        console.print(f"[red]Environment setup failed: {str(e)}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    # Setup environment
    config_dir = setup_environment()

    # Display startup banner
    console.print("""
[bold cyan]🤖 AMP CLI Advanced[/bold cyan]
[bold white]Complete Multi-agent Programming Assistant[/bold white]
[dim]Powered by Gemini 2.0 Flash with Advanced Sub-agent Orchestration[/dim]
""")

    # Run main application
    try:
        asyncio.run(main())
    except Exception as e:
        console.print(f"[red]Application failed to start: {str(e)}[/red]")
        sys.exit(1)
#!/usr/bin/env python3
"""
Simple validation script for AMP CLI Advanced system
Checks that all components are properly implemented
"""

import os
import sys
import ast
import inspect
from pathlib import Path

def validate_main_file():
    """Validate the main.py file structure"""
    print("🔍 Validating main.py structure...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        # Find all classes and functions
        classes = []
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                functions.append(node.name)
        
        print(f"✅ Found {len(classes)} classes")
        print(f"✅ Found {len(functions)} functions")
        
        # Check for key classes
        required_classes = [
            "AmpCLIAdvanced", "EnhancedSubAgent", "TaskOrchestrator",
            "ContextCompressor", "TokenBudgetManager", "CLIInterface"
        ]
        
        missing_classes = [cls for cls in required_classes if cls not in classes]
        if missing_classes:
            print(f"❌ Missing classes: {missing_classes}")
            return False
        else:
            print("✅ All required classes present")
        
        # Check for key functions
        required_functions = [
            "main", "setup_environment", "_detect_project_type",
            "_calculate_complexity", "_load_agent_md_config"
        ]
        
        missing_functions = [func for func in required_functions if func not in functions]
        if missing_functions:
            print(f"❌ Missing functions: {missing_functions}")
            return False
        else:
            print("✅ All required functions present")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating main.py: {str(e)}")
        return False

def validate_imports():
    """Validate that all imports work"""
    print("\n📦 Validating imports...")
    
    try:
        # Test basic imports
        import os, sys, json, time, asyncio, threading
        print("✅ Standard library imports work")
        
        # Test Rich
        from rich.console import Console
        from rich.table import Table
        from rich.panel import Panel
        print("✅ Rich imports work")
        
        # Test pathlib
        from pathlib import Path
        print("✅ Pathlib works")
        
        # Test datetime
        from datetime import datetime
        print("✅ Datetime works")
        
        # Test uuid
        import uuid
        print("✅ UUID works")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False

def validate_file_structure():
    """Validate project file structure"""
    print("\n📁 Validating file structure...")
    
    required_files = [
        "main.py", "requirements.txt", "README.md", 
        "AGENT.md", "test_system.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file} exists")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    return True

def count_tools():
    """Count the number of tools implemented"""
    print("\n🛠️ Counting implemented tools...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Count tool registrations
        tool_registrations = content.count("self.tool_manager.register_tool(")
        print(f"✅ Found {tool_registrations} tool registrations")
        
        # Check if we have 50+ tools as claimed
        if tool_registrations >= 50:
            print("✅ 50+ tools implemented as promised")
        else:
            print(f"⚠️ Only {tool_registrations} tools found (claimed 50+)")
        
        return tool_registrations
        
    except Exception as e:
        print(f"❌ Error counting tools: {str(e)}")
        return 0

def validate_todo_completion():
    """Check that all TODO items are completed"""
    print("\n📋 Checking TODO completion...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        todo_lines = []
        for i, line in enumerate(lines, 1):
            if "TODO" in line.upper() or "FIXME" in line.upper():
                todo_lines.append((i, line.strip()))
        
        if todo_lines:
            print(f"❌ Found {len(todo_lines)} TODO items:")
            for line_num, line in todo_lines:
                print(f"  Line {line_num}: {line}")
            return False
        else:
            print("✅ No TODO items found - all completed")
            return True
            
    except Exception as e:
        print(f"❌ Error checking TODOs: {str(e)}")
        return False

def validate_completeness():
    """Check that the file is complete (no truncation)"""
    print("\n📄 Checking file completeness...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check if file ends properly
        if content.endswith('sys.exit(1)'):
            print("✅ File appears complete")
            return True
        else:
            print("❌ File may be incomplete or truncated")
            return False
            
    except Exception as e:
        print(f"❌ Error checking completeness: {str(e)}")
        return False

def main():
    """Run all validations"""
    print("🚀 AMP CLI Advanced - System Validation\n")
    
    validations = [
        ("File Structure", validate_file_structure),
        ("Imports", validate_imports),
        ("Main File Structure", validate_main_file),
        ("TODO Completion", validate_todo_completion),
        ("File Completeness", validate_completeness),
    ]
    
    results = {}
    for name, validator in validations:
        results[name] = validator()
    
    # Count tools
    tool_count = count_tools()
    
    # Summary
    print("\n" + "="*60)
    print("📊 VALIDATION SUMMARY")
    print("="*60)
    
    passed = sum(results.values())
    total = len(results)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\n🛠️ Tools Implemented: {tool_count}")
    print(f"📈 Validation Score: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total and tool_count >= 50:
        print("\n🎉 SYSTEM VALIDATION SUCCESSFUL!")
        print("✅ All TODO items completed")
        print("✅ Complete sub-agent system implemented")
        print("✅ Token optimization features active")
        print("✅ 50+ tools available")
        print("✅ Production-ready architecture")
        print("\n🚀 System is ready for use!")
        return True
    else:
        print(f"\n⚠️ VALIDATION INCOMPLETE ({passed}/{total} checks passed)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# AMP CLI Advanced Configuration

## Project Overview
This is the AMP CLI Advanced system - a comprehensive multi-agent programming assistant with advanced sub-agent orchestration and token optimization.

## Architecture
- **Main Agent**: Coordinates overall execution and user interaction
- **Sub-Agents**: Specialized agents for different task categories
- **Task Orchestrator**: Manages parallel execution and workflow coordination
- **Token Manager**: Optimizes token usage across all agents

## Development Guidelines

### Code Style
- Follow PEP 8 for Python code
- Use type hints for all function parameters and return values
- Implement comprehensive error handling
- Add docstrings for all classes and methods

### Testing
- Run tests with: `python -m pytest`
- Use `python main.py --parallel "run tests | check lint | analyze code"`
- Test coverage should be > 90%

### Build Commands
- **Development**: `python main.py` (interactive mode)
- **Testing**: `python main.py -t "run all tests"`
- **Linting**: `python main.py -t "run lint check on all files"`
- **Optimization**: `python main.py --optimize`

### Sub-Agent Specializations
- **File Specialist**: File operations, directory management
- **Code Specialist**: Code analysis, refactoring, optimization
- **Test Specialist**: Testing, debugging, quality assurance
- **Web Specialist**: Web scraping, API calls, online resources
- **Terminal Specialist**: Command execution, system operations
- **Oracle**: Complex reasoning, analysis, strategic planning

### Token Optimization
- Use context compression for large files
- Implement smart caching for repeated operations
- Allocate budgets based on task complexity
- Monitor and optimize token usage continuously

### Workflow Patterns
- **Parallel Execution**: Use for independent tasks
- **Sequential Dependencies**: Use workflow orchestration
- **Complex Analysis**: Delegate to Oracle agent
- **Multi-step Processes**: Use TaskOrchestrator

## Common Commands
- `python main.py --parallel "task1 | task2 | task3"` - Parallel execution
- `python main.py --workflow "complex multi-step process"` - Workflow orchestration
- `python main.py --optimize` - Token-efficient mode
- `python main.py -t "analyze and refactor codebase"` - Single complex task

## Error Handling
- All functions include comprehensive try-catch blocks
- Graceful degradation for failed sub-agents
- Automatic retry mechanisms for transient failures
- Detailed error reporting and recovery suggestions

## Performance Targets
- Token efficiency: 40%+ reduction compared to baseline
- Parallel speedup: Up to 8x for parallelizable tasks
- Context compression: 60%+ size reduction while preserving key information
- Response time: < 30 seconds for most operations
